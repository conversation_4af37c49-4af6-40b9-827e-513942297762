# WataBot Admin Dashboard

A modern water level monitoring system dashboard built with Nuxt 3, inspired by smart home app designs. This admin dashboard allows monitoring of multiple water level devices, tracking their levels, locations, and usage trends.

## Features

### 🏠 Main Dashboard
- **Device Overview**: Grid layout showing all water level devices
- **Real-time Status**: Visual indicators for device status (normal, low, critical)
- **Online/Offline Status**: Live connection status for each device
- **Water Conservation Metrics**: Overall water status with percentage and remaining time
- **Location-based Filtering**: Filter devices by location (Rooftop, Basement, Backyard, etc.)
- **Recent Activity**: Track recent water usage and device activity

### 📊 Device Detail View
- **Usage Summary**: Monthly water usage trends with interactive charts
- **Historical Data**: Visual representation of water consumption over time
- **Connected Devices**: List of sensors and controllers connected to each tank
- **Cost Tracking**: Monthly expenses and usage statistics
- **Device Management**: Add and manage connected sensors

### 🎨 Design Features
- **Modern UI**: Clean, card-based design inspired by smart home apps
- **Dark Mode Support**: Automatic dark/light theme switching
- **Mobile Responsive**: Optimized for mobile and desktop viewing
- **Smooth Animations**: Hover effects and transitions for better UX
- **Bottom Navigation**: Mobile-friendly navigation bar

## Technology Stack

- **Framework**: Nuxt 3
- **Styling**: Tailwind CSS v4
- **UI Components**: shadcn-vue
- **Icons**: Lucide Icons
- **State Management**: Pinia
- **Animations**: Motion-v
- **TypeScript**: Full TypeScript support

## Device Data Structure

Each water level device includes:
- Device ID and name
- Location information
- Current water level percentage
- Maximum capacity in liters
- Status (normal, low, critical)
- Online/offline status
- Connected device count
- Last updated timestamp

## Usage Metrics

The dashboard tracks:
- Real-time water levels
- Monthly usage patterns
- Cost analysis
- Device performance
- Conservation metrics
- Historical trends

## Getting Started

1. **Install Dependencies**
   ```bash
   npm install
   ```

2. **Run Development Server**
   ```bash
   npm run dev
   ```

3. **Build for Production**
   ```bash
   npm run build
   ```

## Project Structure

```
├── pages/
│   ├── index.vue              # Main dashboard
│   └── device/
│       └── [id].vue           # Device detail view
├── components/
│   └── ui/                    # shadcn-vue components
├── assets/
│   └── css/
│       └── tailwind.css       # Global styles
└── types/                     # TypeScript definitions
```

## Key Components

### Main Dashboard (`pages/index.vue`)
- Device grid with status indicators
- Overall water conservation metrics
- Location-based filtering
- Recent activity feed
- Bottom navigation

### Device Detail (`pages/device/[id].vue`)
- Usage trend charts
- Monthly statistics
- Connected device management
- Historical data visualization

## Customization

The dashboard can be easily customized for different water monitoring needs:
- Add new device types
- Modify status thresholds
- Customize location categories
- Extend metrics and analytics
- Add notification systems

## Future Enhancements

Potential features for expansion:
- Real-time WebSocket connections
- Push notifications for critical levels
- Advanced analytics and reporting
- Multi-user access control
- Integration with IoT sensors
- Automated alerts and scheduling
- Export functionality for reports

## Screenshots

The dashboard design is inspired by modern smart home applications, featuring:
- Clean card-based layouts
- Intuitive status indicators
- Interactive charts and graphs
- Mobile-first responsive design
- Smooth animations and transitions

## License

This project is built for water level monitoring and can be adapted for various IoT monitoring applications.
