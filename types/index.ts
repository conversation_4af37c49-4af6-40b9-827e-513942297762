export interface Tank {
  id: number
  name: string
  brand: string
  height: number // meters
  volume: number // liters
  currentLevel: number // percentage (0-100)
  status: 'normal' | 'low' | 'critical'
  lastUpdated: Date
  isOnline: boolean
  sensorId: string
}

export interface Location {
  id: number
  name: string
  description: string
  tanks: Tank[]
  icon: string
}

export interface RecentActivity {
  id: number
  tankName: string
  activity: string
  time: Date
  type: 'success' | 'warning' | 'error' | 'info'
  icon: string
}

export interface UsageData {
  month: string
  usage: number // liters
}

export interface ConnectedDevice {
  name: string
  duration: string
  deviceCount: number
  usage: string
  icon: string
}

export interface DeviceDetail extends WaterDevice {
  monthlyUsage: number // liters
  monthlyExpenses: number // dollars
  usageHistory: UsageData[]
  connectedDevices: ConnectedDevice[]
}

export interface DashboardStats {
  totalCapacity: number
  totalCurrentLevel: number
  overallPercentage: number
  activeDevices: number
  offlineDevices: number
}

export interface Organization {
  name: string
  logo: string
  plan: string
  totalTanks: number
  activeSensors: number
}

export interface RealTimeReading {
  timestamp: Date
  level: number // percentage
  volume: number // liters
  temperature?: number // celsius
  ph?: number
}

export type DeviceStatus = 'normal' | 'low' | 'critical'
export type DeviceLocation = 'Rooftop' | 'Basement' | 'Backyard' | 'Utility Room' | 'Kitchen' | 'Garden'
