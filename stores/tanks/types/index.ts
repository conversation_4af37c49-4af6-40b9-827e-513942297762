export interface Tank {
  id: number
  name: string
  brand: string
  manufacturer: string
  height: number
  volume: number
  currentLevel: number
  status: 'normal' | 'low' | 'critical' | 'empty' | 'full'
  lastUpdated: Date
  isOnline: boolean
  sensorId: string
  location: string
  locationId: number
  monthlyUsage: number
  monthlyExpenses: number
  sensor: SensorInfo
}

export interface SensorInfo {
  name: string
  type: string
  model: string
  technology: string
  imei: string
  gsmNumber: string
  installDate: Date
  lastMaintenance: Date
  batteryLevel: number
  signalStrength: number
}

export interface RealTimeReading {
  timestamp: Date
  level: number
  volume: number
  temperature?: number
  ph?: number
  pressure?: number
  flow?: number
}

export interface UsageData {
  month: string
  usage: number
  cost: number
  efficiency: number
}

export interface ConnectedDevice {
  name: string
  duration: string
  deviceCount: number
  usage: string
  icon: string
  status: 'active' | 'inactive' | 'maintenance'
  lastSeen: Date
}

export interface TankActivity {
  id: number
  tankId: number
  tankName: string
  location: string
  action: string
  timestamp: Date
  type: 'info' | 'warning' | 'error' | 'success'
  severity: 'low' | 'medium' | 'high'
  description: string
}

export interface TankStats {
  totalTanks: number
  fullTanks: number
  almostEmptyTanks: number
  emptyTanks: number
  criticalTanks: number
  lowTanks: number
  normalTanks: number
  offlineTanks: number
  averageLevel: number
  totalCapacity: number
  currentVolume: number
}

export interface TankState {
  tanks: Map<number, Tank>
  realTimeReadings: Map<number, RealTimeReading[]>
  currentReadings: Map<number, RealTimeReading>
  usageData: Map<number, UsageData[]>
  connectedDevices: Map<number, ConnectedDevice[]>
  activities: TankActivity[]
  loading: boolean
  error: string | null
}
