import { defineStore } from 'pinia'
import type { Tank, RealTimeReading, UsageData, ConnectedDevice, TankActivity, TankStats, SensorInfo } from './types'
import { supabase, getWaterLevel, calculateVolumeFromLevel, calculateWaterLevel, calculateVolume, getTankStatus, WASL_TANK_CONFIG, type DeviceMetric } from '~/lib/supabase'

export const useTanksStore = defineStore('tanks', () => {
  // State
  const tanks = ref<Map<number, Tank>>(new Map())
  const realTimeReadings = ref<Map<number, RealTimeReading[]>>(new Map())
  const currentReadings = ref<Map<number, RealTimeReading>>(new Map())
  const usageData = ref<Map<number, UsageData[]>>(new Map())
  const connectedDevices = ref<Map<number, ConnectedDevice[]>>(new Map())
  const activities = ref<TankActivity[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  const supabaseSubscription = ref<any>(null)

  // Initialize mock data with updated information
  const initializeTankData = () => {
    const sensorInfo: SensorInfo = {
      name: 'Level Sensor',
      type: 'DF555',
      model: 'DF555',
      technology: 'Ultrasonic Level Sensor',
      imei: '860761079932543',
      gsmNumber: '+233558474469',
      installDate: new Date('2023-06-15'),
      lastMaintenance: new Date('2024-01-15'),
      batteryLevel: 85,
      signalStrength: 92
    }

    const mockTank: Tank = {
      id: 101,
      name: 'WASL Main Tank',
      brand: 'Sintex Tank',
      manufacturer: 'Rambo 200',
      height: 2.5,
      volume: 2000,
      currentLevel: 75,
      status: 'normal',
      lastUpdated: new Date(),
      isOnline: true,
      sensorId: 'DF555-001',
      location: 'Wise As A Serpent',
      locationId: 1,
      monthlyUsage: 1500,
      monthlyExpenses: 450,
      sensor: sensorInfo
    }

    tanks.value.set(mockTank.id, mockTank)
    
    // Initialize real-time readings
    const readings: RealTimeReading[] = []
    for (let i = 19; i >= 0; i--) {
      readings.push({
        timestamp: new Date(Date.now() - i * 30000),
        level: mockTank.currentLevel + (Math.random() - 0.5) * 5,
        volume: Math.round((mockTank.currentLevel / 100) * mockTank.volume),
        temperature: 24 + (Math.random() - 0.5) * 2,
        ph: 7.2 + (Math.random() - 0.5) * 0.3,
        pressure: 1.2 + (Math.random() - 0.5) * 0.1,
        flow: 15 + (Math.random() - 0.5) * 3
      })
    }
    realTimeReadings.value.set(mockTank.id, readings)
    currentReadings.value.set(mockTank.id, readings[readings.length - 1])

    // Initialize usage data
    usageData.value.set(mockTank.id, [
      { month: 'Jan', usage: 1200, cost: 360, efficiency: 88 },
      { month: 'Feb', usage: 1350, cost: 405, efficiency: 92 },
      { month: 'Mar', usage: 1280, cost: 384, efficiency: 90 },
      { month: 'Apr', usage: 1450, cost: 435, efficiency: 85 },
      { month: 'May', usage: 1600, cost: 480, efficiency: 87 },
      { month: 'Jun', usage: 1520, cost: 456, efficiency: 91 },
      { month: 'Jul', usage: 1500, cost: 450, efficiency: 89 }
    ])

    // Initialize connected devices
    connectedDevices.value.set(mockTank.id, [
      {
        name: 'Level Sensor DF555',
        duration: '24h 15m',
        deviceCount: 1,
        usage: '12kWh',
        icon: 'gauge',
        status: 'active',
        lastSeen: new Date()
      },
     
    ])

    // Initialize activities
    activities.value = [
      {
        id: 1,
        tankId: 101,
        tankName: 'WASL Main Tank',
        location: 'Wise As A Serpent',
        action: 'Water level normal at 75%',
        timestamp: new Date(Date.now() - 5 * 60000),
        type: 'info',
        severity: 'low',
        description: 'Tank water level is within normal operating range'
      },
      {
        id: 2,
        tankId: 101,
        tankName: 'WASL Main Tank',
        location: 'Wise As A Serpent',
        action: 'Sensor maintenance completed',
        timestamp: new Date(Date.now() - 2 * 60 * 60000),
        type: 'success',
        severity: 'low',
        description: 'Ultrasonic level sensor DF555 maintenance completed successfully'
      },
      {
        id: 3,
        tankId: 101,
        tankName: 'WASL Main Tank',
        location: 'Wise As A Serpent',
        action: 'Daily usage report generated',
        timestamp: new Date(Date.now() - 4 * 60 * 60000),
        type: 'info',
        severity: 'low',
        description: 'Daily water usage: 85L, efficiency: 89%'
      }
    ]
  }

  // Initialize data on store creation
  initializeTankData()

  // Getters
  const allTanks = computed(() => Array.from(tanks.value.values()))
  
  const tankStats = computed((): TankStats => {
    const tankList = allTanks.value
    const totalTanks = tankList.length
    const fullTanks = tankList.filter(t => t.currentLevel >= 90).length
    const almostEmptyTanks = tankList.filter(t => t.currentLevel <= 20 && t.currentLevel > 0).length
    const emptyTanks = tankList.filter(t => t.currentLevel === 0).length
    const criticalTanks = tankList.filter(t => t.status === 'critical').length
    const lowTanks = tankList.filter(t => t.status === 'low').length
    const normalTanks = tankList.filter(t => t.status === 'normal').length
    const offlineTanks = tankList.filter(t => !t.isOnline).length
    
    const totalCapacity = tankList.reduce((sum, tank) => sum + tank.volume, 0)
    const currentVolume = tankList.reduce((sum, tank) => sum + (tank.currentLevel / 100 * tank.volume), 0)
    const averageLevel = totalTanks > 0 ? Math.round(tankList.reduce((sum, tank) => sum + tank.currentLevel, 0) / totalTanks) : 0

    return {
      totalTanks,
      fullTanks,
      almostEmptyTanks,
      emptyTanks,
      criticalTanks,
      lowTanks,
      normalTanks,
      offlineTanks,
      averageLevel,
      totalCapacity,
      currentVolume
    }
  })

  const getTankById = (id: number) => tanks.value.get(id)
  const getCurrentReading = (tankId: number) => currentReadings.value.get(tankId)
  const getRealTimeReadings = (tankId: number) => realTimeReadings.value.get(tankId) || []
  const getUsageData = (tankId: number) => usageData.value.get(tankId) || []
  const getConnectedDevices = (tankId: number) => connectedDevices.value.get(tankId) || []
  const getTanksByLocation = (location: string) => allTanks.value.filter(tank => tank.location === location)
  const getRecentActivities = (limit: number = 10) => activities.value.slice(0, limit)

  // Actions
  const updateRealTimeReading = (tankId: number) => {
    const tank = tanks.value.get(tankId)
    const currentReading = currentReadings.value.get(tankId)
    
    if (!tank || !currentReading) return

    const newReading: RealTimeReading = {
      timestamp: new Date(),
      level: Math.max(0, Math.min(100, currentReading.level + (Math.random() - 0.5) * 2)),
      volume: 0,
      temperature: 24 + (Math.random() - 0.5) * 3,
      ph: 7 + (Math.random() - 0.5) * 0.5,
      pressure: 1.2 + (Math.random() - 0.5) * 0.1,
      flow: 15 + (Math.random() - 0.5) * 3
    }
    
    newReading.volume = Math.round((newReading.level / 100) * tank.volume)
    
    // Update tank status based on level
    let status: Tank['status'] = 'normal'
    if (newReading.level === 0) status = 'empty'
    else if (newReading.level >= 95) status = 'full'
    else if (newReading.level < 10) status = 'critical'
    else if (newReading.level < 25) status = 'low'
    
    // Update tank
    tank.currentLevel = newReading.level
    tank.status = status
    tank.lastUpdated = newReading.timestamp
    
    // Update current reading
    currentReadings.value.set(tankId, newReading)
    
    // Update readings history (keep last 20)
    const readings = realTimeReadings.value.get(tankId) || []
    readings.unshift(newReading)
    if (readings.length > 20) {
      readings.pop()
    }
    realTimeReadings.value.set(tankId, readings)

    // Add activity if status changed
    if (status !== 'normal') {
      addActivity({
        tankId,
        tankName: tank.name,
        location: tank.location,
        action: `Tank ${status} - ${Math.round(newReading.level)}%`,
        type: status === 'critical' || status === 'empty' ? 'error' : status === 'low' ? 'warning' : 'info',
        severity: status === 'critical' || status === 'empty' ? 'high' : status === 'low' ? 'medium' : 'low',
        description: `Tank water level is ${Math.round(newReading.level)}% (${Math.round(newReading.volume)}L)`
      })
    }
  }

  const addActivity = (activity: Omit<TankActivity, 'id' | 'timestamp'>) => {
    const newActivity: TankActivity = {
      ...activity,
      id: Date.now(),
      timestamp: new Date()
    }
    activities.value.unshift(newActivity)
    // Keep only last 50 activities
    if (activities.value.length > 50) {
      activities.value = activities.value.slice(0, 50)
    }
  }

  const startRealTimeUpdates = (tankId: number) => {
    return setInterval(() => {
      updateRealTimeReading(tankId)
    }, 5000)
  }

  const updateTank = (id: number, updates: Partial<Tank>) => {
    const tank = tanks.value.get(id)
    if (tank) {
      tanks.value.set(id, { ...tank, ...updates })
    }
  }

  // Supabase real-time functions
  const updateTankFromSupabase = (deviceMetric: DeviceMetric) => {
    const tank = tanks.value.get(WASL_TANK_CONFIG.tank_id)
    if (!tank) return

    // Use level column if available, otherwise fall back to tank_height
    let waterLevel: number
    let volume: number

    if (deviceMetric.level !== undefined) {
      // Use level column (already in percentage)
      waterLevel = getWaterLevel(deviceMetric.level)
      volume = calculateVolumeFromLevel(deviceMetric.level)
    } else if (deviceMetric.tank_height !== undefined) {
      // Fall back to tank_height column
      waterLevel = calculateWaterLevel(deviceMetric.tank_height)
      volume = calculateVolume(deviceMetric.tank_height)
    } else {
      console.error('No level or tank_height data found in device metric')
      return
    }

    const status = getTankStatus(waterLevel)
    const timestamp = new Date(deviceMetric.timestamp || deviceMetric.updated_at)

    // Update tank data
    const updatedTank: Tank = {
      ...tank,
      currentLevel: waterLevel,
      status,
      lastUpdated: timestamp,
      isOnline: true
    }

    tanks.value.set(WASL_TANK_CONFIG.tank_id, updatedTank)

    // Create new real-time reading
    const newReading: RealTimeReading = {
      timestamp,
      level: waterLevel,
      volume,
      temperature: 24 + (Math.random() - 0.5) * 3,
      ph: 7 + (Math.random() - 0.5) * 0.5,
      pressure: 1.2 + (Math.random() - 0.5) * 0.1,
      flow: 15 + (Math.random() - 0.5) * 3
    }

    // Update current reading
    currentReadings.value.set(WASL_TANK_CONFIG.tank_id, newReading)

    // Update readings history (keep last 20)
    const readings = realTimeReadings.value.get(WASL_TANK_CONFIG.tank_id) || []
    readings.unshift(newReading)
    if (readings.length > 20) {
      readings.pop()
    }
    realTimeReadings.value.set(WASL_TANK_CONFIG.tank_id, readings)

    // Add activity for significant changes
    if (status !== 'normal') {
      const dataSource = deviceMetric.level !== undefined ?
        `Level: ${deviceMetric.level}%` :
        `Height: ${deviceMetric.tank_height}m`

      addActivity({
        tankId: WASL_TANK_CONFIG.tank_id,
        tankName: WASL_TANK_CONFIG.name,
        location: 'Wise As A Serpent',
        action: `Tank ${status} - ${Math.round(waterLevel)}%`,
        type: status === 'critical' || status === 'empty' ? 'error' : status === 'low' ? 'warning' : 'info',
        severity: status === 'critical' || status === 'empty' ? 'high' : status === 'low' ? 'medium' : 'low',
        description: `Tank water level is ${Math.round(waterLevel)}% (${Math.round(volume)}L) - ${dataSource}`
      })
    }
  }


  const fetchLatestDeviceMetric = async () => {
    try {

      const { data, error: fetchError } = await supabase
        .from('device_metrics')
        .select('*')
        .eq('id', 34)
        .single()

      if (fetchError) {
        console.error('Error fetching device metric:', fetchError)
        throw fetchError;
      }

      return data;

    } catch (err) {
      console.error('Error in fetchLatestDeviceMetric:', err)
      throw err;
    } 
  }

  return {
    // State
    tanks: readonly(tanks),
    activities: readonly(activities),
    loading: readonly(loading),
    error: readonly(error),

    // Getters
    allTanks,
    tankStats,
    getTankById,
    getCurrentReading,
    getRealTimeReadings,
    getUsageData,
    getConnectedDevices,
    getTanksByLocation,
    getRecentActivities,

    // Actions
    updateRealTimeReading,
    addActivity,
    startRealTimeUpdates,
    updateTank,
    fetchLatestDeviceMetric

  }
})
