import { defineStore } from 'pinia'
import type { Location } from '~/types'

export const useLocationsStore = defineStore('locations', () => {
  // State
  const locations = ref<Location[]>([
    {
      id: 1,
      name: 'Rooftop',
      description: 'Main building rooftop tanks',
      tankCount: 2,
      averageLevel: 78,
      status: 'normal',
      isOnline: true
    },
    {
      id: 2,
      name: 'Basement',
      description: 'Underground storage facility',
      tankCount: 1,
      averageLevel: 45,
      status: 'low',
      isOnline: true
    },
    {
      id: 3,
      name: 'Backyard',
      description: 'Outdoor storage area',
      tankCount: 1,
      averageLevel: 92,
      status: 'normal',
      isOnline: false
    },
    {
      id: 4,
      name: 'Utility Room',
      description: 'Indoor utility storage',
      tankCount: 1,
      averageLevel: 23,
      status: 'critical',
      isOnline: true
    }
  ])

  const loading = ref(false)
  const error = ref<string | null>(null)

  // Getters
  const allLocations = computed(() => locations.value)
  const onlineLocations = computed(() => locations.value.filter(loc => loc.isOnline))
  const offlineLocations = computed(() => locations.value.filter(loc => !loc.isOnline))
  const totalTanks = computed(() => locations.value.reduce((sum, loc) => sum + loc.tankCount, 0))
  const averageLevel = computed(() => {
    const total = locations.value.reduce((sum, loc) => sum + loc.averageLevel, 0)
    return Math.round(total / locations.value.length)
  })

  // Actions
  const getLocationById = (id: number) => {
    return locations.value.find(loc => loc.id === id)
  }

  const updateLocation = (id: number, updates: Partial<Location>) => {
    const index = locations.value.findIndex(loc => loc.id === id)
    if (index !== -1) {
      locations.value[index] = { ...locations.value[index], ...updates }
    }
  }

  const updateLocationStatus = (id: number, status: Location['status']) => {
    updateLocation(id, { status })
  }

  const updateLocationLevel = (id: number, averageLevel: number) => {
    const status = averageLevel < 20 ? 'critical' : averageLevel < 40 ? 'low' : 'normal'
    updateLocation(id, { averageLevel, status })
  }

  const toggleLocationOnline = (id: number) => {
    const location = getLocationById(id)
    if (location) {
      updateLocation(id, { isOnline: !location.isOnline })
    }
  }

  const fetchLocations = async () => {
    loading.value = true
    error.value = null
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      // In real app, this would be an actual API call
    } catch (err) {
      error.value = 'Failed to fetch locations'
    } finally {
      loading.value = false
    }
  }

  return {
    // State
    locations: readonly(locations),
    loading: readonly(loading),
    error: readonly(error),
    
    // Getters
    allLocations,
    onlineLocations,
    offlineLocations,
    totalTanks,
    averageLevel,
    
    // Actions
    getLocationById,
    updateLocation,
    updateLocationStatus,
    updateLocationLevel,
    toggleLocationOnline,
    fetchLocations
  }
})
