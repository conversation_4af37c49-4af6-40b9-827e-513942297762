export interface Location {
  id: number
  name: string
  description: string
  tankCount: number
  averageLevel: number
  status: 'normal' | 'low' | 'critical'
  isOnline: boolean
}

export interface LocationState {
  locations: Location[]
  loading: boolean
  error: string | null
}

export interface LocationFilters {
  status?: Location['status']
  isOnline?: boolean
  search?: string
}

export interface LocationStats {
  totalLocations: number
  onlineLocations: number
  offlineLocations: number
  totalTanks: number
  averageLevel: number
  criticalLocations: number
  lowLocations: number
  normalLocations: number
}
