import { defineStore } from 'pinia'
import type { Tank, RealTimeReading, UsageData, ConnectedDevice } from '~/types'

export const useTanksStore = defineStore('tanks', () => {
  // State
  const tanks = ref<Map<number, Tank & { location: string; monthlyUsage: number; monthlyExpenses: number }>>(new Map())
  const realTimeReadings = ref<Map<number, RealTimeReading[]>>(new Map())
  const currentReadings = ref<Map<number, RealTimeReading>>(new Map())
  const usageData = ref<Map<number, UsageData[]>>(new Map())
  const connectedDevices = ref<Map<number, ConnectedDevice[]>>(new Map())
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Initialize mock data
  const initializeTankData = () => {
    // Mock tanks data
    const mockTanks = [
      {
        id: 101,
        name: 'Main Water Tank',
        brand: 'Sintex',
        height: 2.5,
        volume: 1000,
        currentLevel: 75,
        status: 'normal' as const,
        lastUpdated: new Date(),
        isOnline: true,
        sensorId: 'SNS-001',
        location: 'Rooftop',
        monthlyUsage: 350,
        monthlyExpenses: 125
      },
      {
        id: 102,
        name: 'Secondary Tank',
        brand: 'Sintex',
        height: 2.0,
        volume: 750,
        currentLevel: 45,
        status: 'low' as const,
        lastUpdated: new Date(),
        isOnline: true,
        sensorId: 'SNS-002',
        location: 'Basement',
        monthlyUsage: 280,
        monthlyExpenses: 95
      }
    ]

    mockTanks.forEach(tank => {
      tanks.value.set(tank.id, tank)
      
      // Initialize real-time readings
      const readings: RealTimeReading[] = []
      for (let i = 19; i >= 0; i--) {
        readings.push({
          timestamp: new Date(Date.now() - i * 30000),
          level: tank.currentLevel + (Math.random() - 0.5) * 5,
          volume: Math.round((tank.currentLevel / 100) * tank.volume),
          temperature: 24 + (Math.random() - 0.5) * 2,
          ph: 7.2 + (Math.random() - 0.5) * 0.3
        })
      }
      realTimeReadings.value.set(tank.id, readings)
      currentReadings.value.set(tank.id, readings[readings.length - 1])

      // Initialize usage data
      usageData.value.set(tank.id, [
        { month: 'Jan', usage: 280 },
        { month: 'Feb', usage: 320 },
        { month: 'Mar', usage: 290 },
        { month: 'Apr', usage: 350 },
        { month: 'May', usage: 380 },
        { month: 'Jun', usage: 340 },
        { month: 'Jul', usage: 360 }
      ])

      // Initialize connected devices
      connectedDevices.value.set(tank.id, [
        {
          name: 'Level Sensor',
          duration: '2h 30m',
          deviceCount: 1,
          usage: '10kWh',
          icon: 'gauge'
        },
        {
          name: 'Flow Meter',
          duration: '2h 30m',
          deviceCount: 1,
          usage: '5kWh',
          icon: 'activity'
        },
        {
          name: 'Temperature Sensor',
          duration: '8h 50m',
          deviceCount: 1,
          usage: '3kWh',
          icon: 'thermometer'
        }
      ])
    })
  }

  // Initialize data on store creation
  initializeTankData()

  // Getters
  const getTankById = (id: number) => {
    return tanks.value.get(id)
  }

  const getCurrentReading = (tankId: number) => {
    return currentReadings.value.get(tankId)
  }

  const getRealTimeReadings = (tankId: number) => {
    return realTimeReadings.value.get(tankId) || []
  }

  const getUsageData = (tankId: number) => {
    return usageData.value.get(tankId) || []
  }

  const getConnectedDevices = (tankId: number) => {
    return connectedDevices.value.get(tankId) || []
  }

  const getTanksByLocation = (location: string) => {
    return Array.from(tanks.value.values()).filter(tank => tank.location === location)
  }

  // Actions
  const updateRealTimeReading = (tankId: number) => {
    const tank = tanks.value.get(tankId)
    const currentReading = currentReadings.value.get(tankId)
    
    if (!tank || !currentReading) return

    const newReading: RealTimeReading = {
      timestamp: new Date(),
      level: Math.max(0, Math.min(100, currentReading.level + (Math.random() - 0.5) * 2)),
      volume: 0,
      temperature: 24 + (Math.random() - 0.5) * 3,
      ph: 7 + (Math.random() - 0.5) * 0.5
    }
    
    newReading.volume = Math.round((newReading.level / 100) * tank.volume)
    
    // Update tank status based on level
    let status: Tank['status'] = 'normal'
    if (newReading.level < 20) status = 'critical'
    else if (newReading.level < 40) status = 'low'
    
    // Update tank
    tank.currentLevel = newReading.level
    tank.status = status
    tank.lastUpdated = newReading.timestamp
    
    // Update current reading
    currentReadings.value.set(tankId, newReading)
    
    // Update readings history (keep last 20)
    const readings = realTimeReadings.value.get(tankId) || []
    readings.unshift(newReading)
    if (readings.length > 20) {
      readings.pop()
    }
    realTimeReadings.value.set(tankId, readings)
  }

  const startRealTimeUpdates = (tankId: number) => {
    return setInterval(() => {
      updateRealTimeReading(tankId)
    }, 5000)
  }

  const updateTank = (id: number, updates: Partial<Tank>) => {
    const tank = tanks.value.get(id)
    if (tank) {
      tanks.value.set(id, { ...tank, ...updates })
    }
  }

  return {
    // State
    tanks: readonly(tanks),
    loading: readonly(loading),
    error: readonly(error),
    
    // Getters
    getTankById,
    getCurrentReading,
    getRealTimeReadings,
    getUsageData,
    getConnectedDevices,
    getTanksByLocation,
    
    // Actions
    updateRealTimeReading,
    startRealTimeUpdates,
    updateTank
  }
})
