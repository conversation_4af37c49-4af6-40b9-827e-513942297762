export interface Organization {
  id: number
  name: string
  logo: string
  plan: 'Basic' | 'Pro' | 'Enterprise'
  totalTanks: number
  activeSensors: number
  totalLocations: number
  establishedDate: Date
  contactEmail: string
  contactPhone: string
  address: string
  subscription: {
    status: 'active' | 'inactive' | 'trial'
    expiryDate: Date
    features: string[]
  }
}

export interface OrganizationState {
  currentOrganization: Organization | null
  loading: boolean
  error: string | null
}

export interface OrganizationStats {
  totalTanks: number
  activeSensors: number
  totalLocations: number
  onlineDevices: number
  offlineDevices: number
  criticalAlerts: number
  monthlyUsage: number
  monthlyCost: number
}
