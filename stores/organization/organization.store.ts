import { defineStore } from 'pinia'
import type { Organization, OrganizationState, OrganizationStats } from './types'

export const useOrganizationStore = defineStore('organization', () => {
  // State
  const currentOrganization = ref<Organization>({
    id: 1,
    name: '<PERSON><PERSON><PERSON><PERSON>',
    logo: 'https://images.unsplash.com/photo-1560179707-f14e90ef3623?w=40&h=40&fit=crop&auto=format',
    plan: 'Enterprise',
    totalTanks: 1,
    activeSensors: 1,
    totalLocations: 1,
    establishedDate: new Date('2020-01-15'),
    contactEmail: '<EMAIL>',
    contactPhone: '+233558474469',
    address: 'Accra, Ghana',
    subscription: {
      status: 'active',
      expiryDate: new Date('2024-12-31'),
      features: ['Real-time Monitoring', 'Analytics', 'Alerts', 'API Access']
    }
  })

  const loading = ref(false)
  const error = ref<string | null>(null)

  // Getters
  const organizationName = computed(() => currentOrganization.value?.name || '')
  const organizationPlan = computed(() => currentOrganization.value?.plan || 'Basic')
  const totalTanks = computed(() => currentOrganization.value?.totalTanks || 0)
  const activeSensors = computed(() => currentOrganization.value?.activeSensors || 0)
  const totalLocations = computed(() => currentOrganization.value?.totalLocations || 0)
  const subscriptionStatus = computed(() => currentOrganization.value?.subscription.status || 'inactive')
  const isSubscriptionActive = computed(() => subscriptionStatus.value === 'active')

  const organizationStats = computed((): OrganizationStats => ({
    totalTanks: totalTanks.value,
    activeSensors: activeSensors.value,
    totalLocations: totalLocations.value,
    onlineDevices: activeSensors.value,
    offlineDevices: 0,
    criticalAlerts: 0,
    monthlyUsage: 2500,
    monthlyCost: 89
  }))

  // Actions
  const updateOrganization = (updates: Partial<Organization>) => {
    if (currentOrganization.value) {
      currentOrganization.value = { ...currentOrganization.value, ...updates }
    }
  }

  const updateTankCount = (count: number) => {
    if (currentOrganization.value) {
      currentOrganization.value.totalTanks = count
    }
  }

  const updateSensorCount = (count: number) => {
    if (currentOrganization.value) {
      currentOrganization.value.activeSensors = count
    }
  }

  const updateLocationCount = (count: number) => {
    if (currentOrganization.value) {
      currentOrganization.value.totalLocations = count
    }
  }

  const fetchOrganization = async () => {
    loading.value = true
    error.value = null
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      // In real app, this would be an actual API call
    } catch (err) {
      error.value = 'Failed to fetch organization data'
    } finally {
      loading.value = false
    }
  }

  return {
    // State
    currentOrganization: readonly(currentOrganization),
    loading: readonly(loading),
    error: readonly(error),
    
    // Getters
    organizationName,
    organizationPlan,
    totalTanks,
    activeSensors,
    totalLocations,
    subscriptionStatus,
    isSubscriptionActive,
    organizationStats,
    
    // Actions
    updateOrganization,
    updateTankCount,
    updateSensorCount,
    updateLocationCount,
    fetchOrganization
  }
})
