<script setup lang="ts">
import { supabase, WASL_TANK_CONFIG } from '~/lib/supabase'

const currentLevel = ref<number | null>(null)
const customLevel = ref<number | null>(null)
const isUpdating = ref(false)
const lastUpdate = ref<Date | null>(null)
const error = ref<string | null>(null)

// Fetch current level
const fetchCurrentLevel = async () => {
  try {
    const { data, error: fetchError } = await supabase
      .from('device_metrics')
      .select('level, updated_at')
      .eq('id', WASL_TANK_CONFIG.entry_id)
      .single()

    if (fetchError) {
      error.value = fetchError.message
      return
    }

    if (data) {
      currentLevel.value = data.level
      lastUpdate.value = new Date(data.updated_at)
      error.value = null
    }
  } catch (err) {
    error.value = 'Failed to fetch current level'
    console.error(err)
  }
}

// Update level in database
const updateLevel = async (newLevel: number) => {
  if (isUpdating.value) return
  
  isUpdating.value = true
  error.value = null
  
  try {
    const { error: updateError } = await supabase
      .from('device_metrics')
      .update({ 
        level: newLevel,
        updated_at: new Date().toISOString()
      })
      .eq('id', WASL_TANK_CONFIG.entry_id)

    if (updateError) {
      error.value = updateError.message
    } else {
      await fetchCurrentLevel()
    }
  } catch (err) {
    error.value = 'Failed to update level'
    console.error(err)
  } finally {
    isUpdating.value = false
  }
}

// Quick level buttons
const quickLevels = [0, 25, 50, 75, 100]

onMounted(() => {
  fetchCurrentLevel()
})
</script>

<template>
  <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Sensor Test Panel</h3>
      <button
        @click="fetchCurrentLevel"
        :disabled="isUpdating"
        class="text-sm text-blue-500 hover:text-blue-600 disabled:opacity-50"
      >
        Refresh
      </button>
    </div>

    <div class="space-y-4">
      <!-- Current Level Display -->
      <div class="text-center p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
        <div class="text-2xl font-bold text-gray-900 dark:text-white mb-1">
          {{ currentLevel !== null ? `${currentLevel}%` : '--' }}
        </div>
        <div class="text-sm text-gray-500 dark:text-gray-400">
          Current Level (Entry ID: {{ WASL_TANK_CONFIG.entry_id }})
        </div>
        <div v-if="lastUpdate" class="text-xs text-gray-400 mt-1">
          Last updated: {{ lastUpdate.toLocaleTimeString() }}
        </div>
      </div>

      <!-- Quick Level Buttons -->
      <div class="grid grid-cols-5 gap-2">
        <button
          v-for="level in quickLevels"
          :key="level"
          @click="updateLevel(level)"
          :disabled="isUpdating"
          :class="[
            'px-3 py-2 text-sm font-medium rounded-md transition-colors',
            currentLevel === level
              ? 'bg-blue-500 text-white'
              : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600',
            isUpdating ? 'opacity-50 cursor-not-allowed' : ''
          ]"
        >
          {{ level }}%
        </button>
      </div>

      <!-- Custom Level Input -->
      <div class="flex space-x-2">
        <input
          v-model.number="customLevel"
          type="number"
          min="0"
          max="100"
          placeholder="Custom level"
          class="flex-1 px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
        />
        <button
          @click="updateLevel(customLevel)"
          :disabled="isUpdating || !customLevel || customLevel < 0 || customLevel > 100"
          class="px-4 py-2 text-sm font-medium bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {{ isUpdating ? 'Updating...' : 'Set' }}
        </button>
      </div>

      <!-- Error Display -->
      <div v-if="error" class="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
        <p class="text-sm text-red-600 dark:text-red-400">{{ error }}</p>
      </div>

      <!-- Instructions -->
      <div class="text-xs text-gray-500 dark:text-gray-400 space-y-1">
        <p><strong>Instructions:</strong></p>
        <p>• Click quick level buttons to set predefined levels</p>
        <p>• Use custom input for specific values (0-100)</p>
        <p>• Changes will trigger real-time updates in the monitoring component</p>
        <p>• Check the browser console for real-time subscription logs</p>
      </div>
    </div>
  </div>
</template>


