<script setup lang="ts">
import type { Location } from '~/stores/location/types'

interface Props {
  location: Location
}

const props = defineProps<Props>()

const emit = defineEmits<{
  click: [location: Location]
}>()

const getStatusColor = (status: Location['status']) => {
  switch (status) {
    case 'normal': return 'text-green-500'
    case 'low': return 'text-yellow-500'
    case 'critical': return 'text-red-500'
    default: return 'text-gray-500'
  }
}

const getStatusBgColor = (status: Location['status']) => {
  switch (status) {
    case 'normal': return 'bg-green-500'
    case 'low': return 'bg-yellow-500'
    case 'critical': return 'bg-red-500'
    default: return 'bg-gray-500'
  }
}
</script>

<template>
  <div 
    @click="emit('click', location)"
    class="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-all duration-200 cursor-pointer"
  >
    <!-- Header -->
    <div class="flex items-center justify-between mb-4">
      <div class="flex items-center space-x-3">
        <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
          <Icon name="lucide:map-pin" class="w-5 h-5 text-blue-600 dark:text-blue-400" />
        </div>
        <div>
          <h3 class="font-semibold text-gray-900 dark:text-white">{{ location.name }}</h3>
          <p class="text-sm text-gray-500 dark:text-gray-400">{{ location.description }}</p>
        </div>
      </div>
      <div :class="['w-3 h-3 rounded-full', location.isOnline ? 'bg-green-500' : 'bg-red-500']"></div>
    </div>

    <!-- Stats -->
    <div class="grid grid-cols-2 gap-4 mb-4">
      <div class="text-center p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
        <div class="text-lg font-bold text-gray-900 dark:text-white">{{ location.tankCount }}</div>
        <div class="text-xs text-gray-500 dark:text-gray-400">Tanks</div>
      </div>
      <div class="text-center p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
        <div class="text-lg font-bold text-gray-900 dark:text-white">{{ location.averageLevel }}%</div>
        <div class="text-xs text-gray-500 dark:text-gray-400">Avg Level</div>
      </div>
    </div>

    <!-- Status Bar -->
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-2">
        <div :class="['w-2 h-2 rounded-full', getStatusBgColor(location.status)]"></div>
        <span :class="['text-sm font-medium capitalize', getStatusColor(location.status)]">
          {{ location.status }}
        </span>
      </div>
      <Icon name="lucide:chevron-right" class="w-4 h-4 text-gray-400" />
    </div>
  </div>
</template>
