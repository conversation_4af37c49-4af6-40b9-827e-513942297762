<script setup lang="ts">
interface Props {
  currentPage?: string
}

const props = withDefaults(defineProps<Props>(), {
  currentPage: 'home'
})

const router = useRouter()

const navigationItems = [
  { id: 'home', icon: 'lucide:home', label: 'Home', path: '/' },
  { id: 'tanks', icon: 'lucide:grid-3x3', label: 'Tanks', path: '/tanks' },
  { id: 'analytics', icon: 'lucide:bar-chart-3', label: 'Analytics', path: '/analytics' },
  { id: 'profile', icon: 'lucide:user', label: 'Profile', path: '/profile' }
]

const navigateTo = (path: string) => {
  router.push(path)
}
</script>

<template>
  <div class="fixed bottom-0 left-0 right-0 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 px-4 py-2">
    <div class="max-w-7xl mx-auto">
      <div class="flex items-center justify-around">
        <button 
          v-for="item in navigationItems"
          :key="item.id"
          @click="navigateTo(item.path)"
          :class="[
            'flex flex-col items-center space-y-1 p-2 transition-colors',
            currentPage === item.id 
              ? 'text-blue-500' 
              : 'text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300'
          ]"
        >
          <Icon :name="item.icon" class="w-6 h-6" />
          <span class="text-xs">{{ item.label }}</span>
        </button>
      </div>
    </div>
  </div>
</template>
