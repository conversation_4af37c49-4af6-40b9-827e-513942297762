<script setup lang="ts">
import type { TankStats } from '~/stores/tanks/types'

interface Props {
  stats: TankStats
}

const props = defineProps<Props>()

const statusCards = computed(() => [
  {
    title: 'Full Tanks',
    count: props.stats.fullTanks,
    icon: 'lucide:droplets',
    color: 'text-blue-600 bg-blue-100',
    description: '≥90% capacity'
  },
  {
    title: 'Normal',
    count: props.stats.normalTanks,
    icon: 'lucide:check-circle',
    color: 'text-green-600 bg-green-100',
    description: '25-89% capacity'
  },
  {
    title: 'Low Level',
    count: props.stats.lowTanks,
    icon: 'lucide:alert-triangle',
    color: 'text-yellow-600 bg-yellow-100',
    description: '10-24% capacity'
  },
  {
    title: 'Critical',
    count: props.stats.criticalTanks,
    icon: 'lucide:alert-circle',
    color: 'text-red-600 bg-red-100',
    description: '1-9% capacity'
  },
  {
    title: 'Empty',
    count: props.stats.emptyTanks,
    icon: 'lucide:circle',
    color: 'text-gray-600 bg-gray-100',
    description: '0% capacity'
  },
  {
    title: 'Offline',
    count: props.stats.offlineTanks,
    icon: 'lucide:wifi-off',
    color: 'text-purple-600 bg-purple-100',
    description: 'No connection'
  }
])

const capacityPercentage = computed(() => {
  if (props.stats.totalCapacity === 0) return 0
  return Math.round((props.stats.currentVolume / props.stats.totalCapacity) * 100)
})
</script>

<template>
  <div class="space-y-6">
    <!-- Overall Capacity -->
    <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl p-6 text-white">
      <div class="flex items-center justify-between mb-4">
        <div>
          <h3 class="text-lg font-semibold">Total Water Capacity</h3>
          <p class="text-blue-100">Across all tanks</p>
        </div>
        <div class="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
          <Icon name="lucide:droplets" class="w-6 h-6" />
        </div>
      </div>
      
      <div class="flex items-end space-x-4">
        <div>
          <div class="text-3xl font-bold">{{ capacityPercentage }}%</div>
          <div class="text-sm text-blue-100">{{ Math.round(stats.currentVolume).toLocaleString() }}L of {{ stats.totalCapacity.toLocaleString() }}L</div>
        </div>
        
        <!-- Capacity Bar -->
        <div class="flex-1">
          <div class="w-full bg-white bg-opacity-20 rounded-full h-2">
            <div 
              class="bg-white rounded-full h-2 transition-all duration-300"
              :style="{ width: `${capacityPercentage}%` }"
            ></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Tank Status Grid -->
    <!-- <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
      <div 
        v-for="card in statusCards" 
        :key="card.title"
        class="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-4 hover:shadow-md transition-shadow"
      >
        <div class="flex items-center justify-between mb-3">
          <div :class="['w-10 h-10 rounded-full flex items-center justify-center', card.color]">
            <Icon :name="card.icon" class="w-5 h-5" />
          </div>
          <div class="text-2xl font-bold text-gray-900 dark:text-white">
            {{ card.count }}
          </div>
        </div>
        
        <div>
          <h4 class="font-medium text-gray-900 dark:text-white mb-1">{{ card.title }}</h4>
          <p class="text-xs text-gray-500 dark:text-gray-400">{{ card.description }}</p>
        </div>
      </div>
    </div> -->

    <!-- Quick Stats -->
    <div class="grid grid-cols-2 md:grid-cols-3 gap-6">
      <!-- <div class="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center space-x-3">
          <div class="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center">
            <Icon name="lucide:gauge" class="w-6 h-6 text-green-600 dark:text-green-400" />
          </div>
          <div>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ stats.averageLevel }}%</p>
            <p class="text-sm text-gray-500 dark:text-gray-400">Average Level</p>
          </div>
        </div>
      </div> -->

      <div class="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center space-x-3">
          <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
            <Icon name="lucide:database" class="w-6 h-6 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ stats.totalTanks }}</p>
            <p class="text-sm text-gray-500 dark:text-gray-400">Total Tanks</p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center space-x-3">
          <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/20 rounded-full flex items-center justify-center">
            <Icon name="lucide:activity" class="w-6 h-6 text-purple-600 dark:text-purple-400" />
          </div>
          <div>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ stats.totalTanks - stats.offlineTanks }}</p>
            <p class="text-sm text-gray-500 dark:text-gray-400">Online Tanks</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
