<script setup lang="ts">
interface Props {
  title: string
  subtitle?: string
  showBackButton?: boolean
  onlineStatus?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showBackButton: false,
  onlineStatus: true
})

const emit = defineEmits<{
  back: []
}>()

import { useOrganizationStore } from '~/stores/organization/organization.store'

const organizationStore = useOrganizationStore()
</script>

<template>
  <div class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex items-center justify-between h-16">
        <div class="flex items-center space-x-4">
          <button 
            v-if="showBackButton"
            @click="emit('back')" 
            class="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg"
          >
            <Icon name="lucide:arrow-left" class="w-5 h-5 text-gray-600 dark:text-gray-300" />
          </button>
          <div>
            <h1 class="text-xl font-semibold text-gray-900 dark:text-white">{{ title }}</h1>
            <p v-if="subtitle" class="text-sm text-gray-500 dark:text-gray-400">
              {{ organizationStore.organizationName }} • {{ subtitle }}
            </p>
          </div>
        </div>
        <div class="flex items-center space-x-4">
          <div v-if="onlineStatus !== undefined" class="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-300">
            <div :class="['w-2 h-2 rounded-full', onlineStatus ? 'bg-green-500' : 'bg-red-500']"></div>
            <span>{{ onlineStatus ? 'ONLINE' : 'OFFLINE' }}</span>
          </div>
          <button class="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg">
            <Icon name="lucide:more-horizontal" class="w-5 h-5 text-gray-600 dark:text-gray-300" />
          </button>
        </div>
      </div>
    </div>
  </div>
</template>
