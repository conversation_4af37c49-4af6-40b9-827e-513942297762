<script setup lang="ts">
import type { TankActivity } from '~/stores/tanks/types'
import { useRelativeTime } from '~/composables/useRelativeTime'

interface Props {
  activities: TankActivity[]
  showAll?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showAll: false
})

const emit = defineEmits<{
  viewAll: []
}>()

const displayedActivities = computed(() => {
  return props.showAll ? props.activities : props.activities.slice(0, 5)
})

const getActivityIcon = (type: TankActivity['type']) => {
  switch (type) {
    case 'success': return 'lucide:check-circle'
    case 'warning': return 'lucide:alert-triangle'
    case 'error': return 'lucide:x-circle'
    case 'info': return 'lucide:info'
    default: return 'lucide:circle'
  }
}

const getActivityColor = (type: TankActivity['type']) => {
  switch (type) {
    case 'success': return 'text-green-600 bg-green-100'
    case 'warning': return 'text-yellow-600 bg-yellow-100'
    case 'error': return 'text-red-600 bg-red-100'
    case 'info': return 'text-blue-600 bg-blue-100'
    default: return 'text-gray-600 bg-gray-100'
  }
}

const getSeverityColor = (severity: TankActivity['severity']) => {
  switch (severity) {
    case 'high': return 'border-l-red-500'
    case 'medium': return 'border-l-yellow-500'
    case 'low': return 'border-l-blue-500'
    default: return 'border-l-gray-500'
  }
}

// Use Luxon-based relative time formatting
const { formatRelativeTime } = useRelativeTime()
</script>

<template>
  <div class="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700">
    <!-- Header -->
    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Recent Activity</h3>
        <button 
          v-if="!showAll && activities.length > 5"
          @click="emit('viewAll')"
          class="text-sm text-blue-500 hover:text-blue-600 font-medium"
        >
          View all
        </button>
      </div>
    </div>

    <!-- Activities List -->
    <div class="divide-y divide-gray-200 dark:divide-gray-700">
      <div 
        v-for="activity in displayedActivities" 
        :key="activity.id"
        :class="['px-6 py-4 border-l-4 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors', getSeverityColor(activity.severity)]"
      >
        <div class="flex items-start space-x-3">
          <!-- Activity Icon -->
          <div :class="['w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0', getActivityColor(activity.type)]">
            <Icon :name="getActivityIcon(activity.type)" class="w-4 h-4" />
          </div>
          
          <!-- Activity Content -->
          <div class="flex-1 min-w-0">
            <div class="flex items-center justify-between">
              <p class="text-sm font-medium text-gray-900 dark:text-white">
                {{ activity.action }}
              </p>
              <span class="text-xs text-gray-500 dark:text-gray-400">
                {{ formatRelativeTime(activity.timestamp) }}
              </span>
            </div>
            
            <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
              {{ activity.tankName }} • {{ activity.location }}
            </p>
            
            <p v-if="activity.description" class="text-xs text-gray-500 dark:text-gray-500 mt-2">
              {{ activity.description }}
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-if="activities.length === 0" class="px-6 py-8 text-center">
      <Icon name="lucide:activity" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
      <p class="text-gray-500 dark:text-gray-400">No recent activity</p>
    </div>
  </div>
</template>
