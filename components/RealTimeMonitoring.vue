<script setup lang="ts">
import type { Tank, RealTimeReading } from '~/stores/tanks/types'

interface Props {
  tank: Tank & { location: string }
  currentReading: RealTimeReading
}

const props = defineProps<Props>()

const getStatusColor = (status: Tank['status']) => {
  switch (status) {
    case 'normal': return 'text-green-500'
    case 'low': return 'text-yellow-500'
    case 'critical': return 'text-red-500'
    default: return 'text-gray-500'
  }
}
</script>

<template>
  <div class="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 mb-6">
    <!-- Header -->
    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
      <div class="flex items-center justify-between">
        <div>
          <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Real-time Monitoring</h2>
          <p class="text-sm text-gray-500 dark:text-gray-400">Live data from {{ tank.sensorId }}</p>
        </div>
        <div class="flex items-center space-x-2 text-sm text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/20 px-3 py-1.5 rounded-full">
          <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
          <span class="font-medium">Live</span>
        </div>
      </div>
    </div>
    
    <!-- Main Content -->
    <div class="p-6">
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Left Section: Details (2/3) -->
        <div class="lg:col-span-2">
          <!-- Current Status -->
          <div class="mb-6">
            <div class="text-center mb-4">
              <div class="text-4xl font-bold text-gray-900 dark:text-white mb-1">{{ Math.round(currentReading.level) }}%</div>
              <div class="text-sm text-gray-500 dark:text-gray-400">Current Water Level</div>
            </div>
            
            <!-- Progress Bar -->
            <div class="relative">
              <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
                <div 
                  :class="['h-3 rounded-full transition-all duration-1000', getStatusColor(tank.status).replace('text-', 'bg-')]"
                  :style="{ width: `${currentReading.level}%` }"
                ></div>
              </div>
              <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
                <span>0%</span>
                <span>25%</span>
                <span>50%</span>
                <span>75%</span>
                <span>100%</span>
              </div>
            </div>
          </div>
          
          <!-- Metrics Grid -->
          <div class="grid grid-cols-3 gap-4 mb-6">
            <div class="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <div class="text-xl font-bold text-blue-600 dark:text-blue-400">{{ Math.round(currentReading.volume) }}L</div>
              <div class="text-xs text-blue-700 dark:text-blue-300">Volume</div>
            </div>
            <div class="text-center p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
              <div class="text-xl font-bold text-orange-600 dark:text-orange-400">{{ currentReading.temperature?.toFixed(1) }}°C</div>
              <div class="text-xs text-orange-700 dark:text-orange-300">Temperature</div>
            </div>
            <div class="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
              <div class="text-xl font-bold text-purple-600 dark:text-purple-400">{{ currentReading.ph?.toFixed(1) }}</div>
              <div class="text-xs text-purple-700 dark:text-purple-300">pH Level</div>
            </div>
          </div>
          
          <!-- Tank Specifications -->
          <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
            <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-3">Tank Specifications</h3>
            <div class="grid grid-cols-2 gap-4">
               <div class="flex justify-between">
                <span class="text-sm text-gray-500 dark:text-gray-400">Manufacturer:</span>
                <span class="text-sm font-medium text-gray-900 dark:text-white">{{ tank.brand }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-sm text-gray-500 dark:text-gray-400">Brand:</span>
                <span class="text-sm font-medium text-gray-900 dark:text-white">Rambo 2000</span>
              </div>
              <div class="flex justify-between">
                <span class="text-sm text-gray-500 dark:text-gray-400">Height:</span>
                <span class="text-sm font-medium text-gray-900 dark:text-white">{{ tank.height }} m</span>
              </div>
              <div class="flex justify-between">
                <span class="text-sm text-gray-500 dark:text-gray-400">Capacity:</span>
                <span class="text-sm font-medium text-gray-900 dark:text-white">{{ tank.volume }} L</span>
              </div>
              <div class="flex justify-between">
                <span class="text-sm text-gray-500 dark:text-gray-400">Sensor:</span>
                <span class="text-sm font-medium text-gray-900 dark:text-white">{{ tank.sensorId }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-sm text-gray-500 dark:text-gray-400">Sensor Reading:</span>
                <span class="text-sm font-medium text-gray-900 dark:text-white">{{ 432 }} cm</span>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Right Section: Vertical Water Level (1/3) -->
        <div class="flex flex-col items-center justify-center">
          <!-- Vertical Tank Visualization -->
          <div class="relative mb-4">
            <div class="w-20 h-64 bg-gray-100 dark:bg-gray-700 rounded-xl relative overflow-hidden border border-gray-300 dark:border-gray-600">
              <!-- Water Level -->
              <div 
                :class="['absolute bottom-0 left-0 right-0 transition-all duration-1000 rounded-b-xl', getStatusColor(tank.status).replace('text-', 'bg-')]"
                :style="{ height: `${currentReading.level}%` }"
              >
                <!-- Water surface animation -->
                <div class="absolute top-0 left-0 right-0 h-1 bg-white bg-opacity-30 animate-pulse"></div>
              </div>
              
              <!-- Tank icon -->
              <div class="absolute inset-0 flex items-center justify-center">
                <Icon name="lucide:droplets" class="w-6 h-6 text-white opacity-80" />
              </div>
              
              <!-- Level markers -->
              <div class="absolute -right-6 top-0 h-full flex flex-col justify-between text-xs text-gray-400">
                <span>100</span>
                <span>75</span>
                <span>50</span>
                <span>25</span>
                <span>0</span>
              </div>
            </div>
          </div>
          
          <!-- Volume info -->
          <div class="text-center">
            <div class="text-lg font-semibold text-gray-900 dark:text-white">{{ Math.round(currentReading.volume) }}L</div>
            <div class="text-sm text-gray-500 dark:text-gray-400">of {{ tank.volume }}L</div>
          </div>
        </div>
      </div>
      
      <!-- Last updated -->
      <div class="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700 text-center">
        <p class="text-xs text-gray-500 dark:text-gray-400">
          Last updated: {{ currentReading.timestamp.toLocaleTimeString() }} • Updates every 5 seconds
        </p>
      </div>
    </div>
  </div>
</template>
