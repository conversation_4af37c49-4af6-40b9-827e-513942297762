<script setup lang="ts">
import { useRelativeTime } from '~/composables/useRelativeTime'

const { formatRelativeTime, formatDetailedTime } = useRelativeTime()

// Sample timestamps for demonstration
const sampleTimes = [
  { label: 'Just now', date: new Date() },
  { label: '30 seconds ago', date: new Date(Date.now() - 30 * 1000) },
  { label: '2 minutes ago', date: new Date(Date.now() - 2 * 60 * 1000) },
  { label: '1 hour ago', date: new Date(Date.now() - 60 * 60 * 1000) },
  { label: '3 hours ago', date: new Date(Date.now() - 3 * 60 * 60 * 1000) },
  { label: 'Yesterday', date: new Date(Date.now() - 24 * 60 * 60 * 1000) },
  { label: '3 days ago', date: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000) },
  { label: 'Last week', date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) },
  { label: '2 weeks ago', date: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000) },
  { label: 'Last month', date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) },
  { label: '3 months ago', date: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000) },
  { label: 'Last year', date: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000) }
]

// Current time for real-time updates
const currentTime = ref(new Date())

// Update current time every second
onMounted(() => {
  const interval = setInterval(() => {
    currentTime.value = new Date()
  }, 1000)

  onUnmounted(() => {
    clearInterval(interval)
  })
})
</script>

<template>
  <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Relative Time Examples</h3>
    
    <div class="space-y-3">
      <div 
        v-for="time in sampleTimes" 
        :key="time.label"
        class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg"
      >
        <div>
          <div class="text-sm font-medium text-gray-900 dark:text-white">
            {{ time.label }}
          </div>
          <div class="text-xs text-gray-500 dark:text-gray-400">
            {{ time.date.toLocaleString() }}
          </div>
        </div>
        <div class="text-right">
          <div class="text-sm font-semibold text-blue-600 dark:text-blue-400">
            {{ formatRelativeTime(time.date) }}
          </div>
        </div>
      </div>
    </div>

    <!-- Live Update Example -->
    <div class="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
      <h4 class="text-md font-medium text-gray-900 dark:text-white mb-3">Live Update Example</h4>
      <div class="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
        <div class="text-sm text-gray-900 dark:text-white">
          Current time: {{ formatRelativeTime(currentTime) }}
        </div>
        <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
          {{ currentTime.toLocaleString() }}
        </div>
      </div>
    </div>

    <!-- Detailed Time Example -->
    <div class="mt-4">
      <h4 class="text-md font-medium text-gray-900 dark:text-white mb-3">Detailed Format Example</h4>
      <div class="p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
        <div class="text-sm space-y-1">
          <div class="text-gray-900 dark:text-white">
            <strong>Relative:</strong> {{ formatDetailedTime(sampleTimes[3].date).relative }}
          </div>
          <div class="text-gray-600 dark:text-gray-400">
            <strong>Absolute:</strong> {{ formatDetailedTime(sampleTimes[3].date).absolute }}
          </div>
          <div class="text-gray-500 dark:text-gray-500 text-xs">
            <strong>ISO:</strong> {{ formatDetailedTime(sampleTimes[3].date).iso }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
