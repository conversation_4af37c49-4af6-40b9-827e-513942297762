<script setup lang="ts">
import type { Tank } from '~/stores/tanks/types'

interface Props {
  tank: Tank & { location?: string }
}

const props = defineProps<Props>()

const emit = defineEmits<{
  click: [tank: Tank]
}>()

const getStatusColor = (status: Tank['status']) => {
  switch (status) {
    case 'normal': return 'text-green-500'
    case 'low': return 'text-yellow-500'
    case 'critical': return 'text-red-500'
    default: return 'text-gray-500'
  }
}

const getStatusBgColor = (status: Tank['status']) => {
  switch (status) {
    case 'normal': return 'bg-green-500'
    case 'low': return 'bg-yellow-500'
    case 'critical': return 'bg-red-500'
    default: return 'bg-gray-500'
  }
}

const formatTimeAgo = (date: Date) => {
  const now = new Date()
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))
  
  if (diffInMinutes < 60) {
    return `${diffInMinutes}m ago`
  } else if (diffInMinutes < 1440) {
    return `${Math.floor(diffInMinutes / 60)}h ago`
  } else {
    return `${Math.floor(diffInMinutes / 1440)}d ago`
  }
}
</script>

<template>
  <div 
    @click="emit('click', tank)"
    class="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-all duration-200 cursor-pointer"
  >
    <!-- Header -->
    <div class="flex items-center justify-between mb-4">
      <div class="flex items-center space-x-3">
        <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
          <Icon name="lucide:droplets" class="w-5 h-5 text-blue-600 dark:text-blue-400" />
        </div>
        <div>
          <h3 class="font-semibold text-gray-900 dark:text-white">{{ tank.name }}</h3>
          <p class="text-sm text-gray-500 dark:text-gray-400">{{ tank.brand }} • {{ tank.volume }}L</p>
        </div>
      </div>
      <div :class="['w-3 h-3 rounded-full', tank.isOnline ? 'bg-green-500' : 'bg-red-500']"></div>
    </div>

    <!-- Water Level -->
    <div class="mb-4">
      <div class="flex justify-between items-center mb-2">
        <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Water Level</span>
        <span class="text-sm font-bold text-gray-900 dark:text-white">{{ Math.round(tank.currentLevel) }}%</span>
      </div>
      <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
        <div 
          :class="['h-2 rounded-full transition-all duration-300', getStatusBgColor(tank.status)]"
          :style="{ width: `${tank.currentLevel}%` }"
        ></div>
      </div>
    </div>

    <!-- Tank Info -->
    <div class="grid grid-cols-2 gap-4 mb-4 text-sm">
      <div>
        <span class="text-gray-500 dark:text-gray-400">Height:</span>
        <span class="ml-1 font-medium text-gray-900 dark:text-white">{{ tank.height }}m</span>
      </div>
      <div>
        <span class="text-gray-500 dark:text-gray-400">Sensor:</span>
        <span class="ml-1 font-medium text-gray-900 dark:text-white">{{ tank.sensorId }}</span>
      </div>
    </div>

    <!-- Status Footer -->
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-2">
        <div :class="['w-2 h-2 rounded-full', getStatusBgColor(tank.status)]"></div>
        <span :class="['text-sm font-medium capitalize', getStatusColor(tank.status)]">
          {{ tank.status }}
        </span>
      </div>
      <span class="text-xs text-gray-500 dark:text-gray-400">
        {{ formatTimeAgo(tank.lastUpdated) }}
      </span>
    </div>
  </div>
</template>
