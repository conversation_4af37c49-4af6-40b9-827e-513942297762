<script setup lang="ts">
import type { UsageData } from '~/stores/tanks/types'

interface Props {
  usageData: UsageData[]
  monthlyUsage: number
  monthlyExpenses: number
}

const props = defineProps<Props>()

const maxPoint = computed(() => Math.max(...props.usageData.map(d => d.usage)))
</script>

<template>
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- Usage Chart -->
    <div class="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Usage Trend</h3>
          <select class="px-3 py-1.5 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg text-sm text-gray-700 dark:text-gray-200">
            <option>Monthly</option>
            <option>Weekly</option>
            <option>Daily</option>
          </select>
        </div>
      </div>
      
      <div class="p-6">
        <!-- Simple Chart -->
        <div class="relative h-40">
          <svg class="w-full h-full" viewBox="0 0 300 120">
            <defs>
              <linearGradient id="chartGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:0.2" />
                <stop offset="100%" style="stop-color:#3B82F6;stop-opacity:0" />
              </linearGradient>
            </defs>
            
            <!-- Grid lines -->
            <g stroke="#E5E7EB" stroke-width="0.5">
              <line x1="0" y1="20" x2="300" y2="20" />
              <line x1="0" y1="40" x2="300" y2="40" />
              <line x1="0" y1="60" x2="300" y2="60" />
              <line x1="0" y1="80" x2="300" y2="80" />
              <line x1="0" y1="100" x2="300" y2="100" />
            </g>
            
            <!-- Area -->
            <path 
              :d="`M 0 ${120 - (usageData[0].usage / maxPoint * 80)} ${usageData.map((d, i) => `L ${i * 45} ${120 - (d.usage / maxPoint * 80)}`).join(' ')} L ${(usageData.length - 1) * 45} 120 L 0 120 Z`"
              fill="url(#chartGradient)"
            />
            
            <!-- Line -->
            <path 
              :d="`M 0 ${120 - (usageData[0].usage / maxPoint * 80)} ${usageData.map((d, i) => `L ${i * 45} ${120 - (d.usage / maxPoint * 80)}`).join(' ')}`"
              stroke="#3B82F6" 
              stroke-width="2" 
              fill="none"
            />
            
            <!-- Points -->
            <circle 
              v-for="(point, index) in usageData" 
              :key="index"
              :cx="index * 45" 
              :cy="120 - (point.usage / maxPoint * 80)"
              r="3" 
              fill="#3B82F6"
            />
          </svg>
          
          <!-- X-axis labels -->
          <div class="absolute bottom-0 left-0 right-0 flex justify-between text-xs text-gray-500 dark:text-gray-400">
            <span v-for="point in usageData" :key="point.month">{{ point.month }}</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Usage Stats -->
    <div class="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Usage Summary</h3>
      </div>
      
      <div class="p-6 space-y-4">
        <div class="flex items-center justify-between p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
              <Icon name="lucide:droplets" class="w-4 h-4 text-white" />
            </div>
            <div>
              <p class="text-sm text-blue-700 dark:text-blue-300">Monthly Usage</p>
              <p class="text-lg font-bold text-blue-900 dark:text-blue-100">{{ monthlyUsage }}L</p>
            </div>
          </div>
        </div>
        
        <div class="flex items-center justify-between p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
              <Icon name="lucide:dollar-sign" class="w-4 h-4 text-white" />
            </div>
            <div>
              <p class="text-sm text-green-700 dark:text-green-300">Monthly Cost</p>
              <p class="text-lg font-bold text-green-900 dark:text-green-100">${{ monthlyExpenses }}</p>
            </div>
          </div>
        </div>
        
        <div class="flex items-center justify-between p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
              <Icon name="lucide:trending-up" class="w-4 h-4 text-white" />
            </div>
            <div>
              <p class="text-sm text-purple-700 dark:text-purple-300">Efficiency</p>
              <p class="text-lg font-bold text-purple-900 dark:text-purple-100">92%</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
