<script setup lang="ts">
import type { Tank } from '~/stores/tanks/types'

interface Props {
  tank: Tank
  showDetails?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showDetails: false
})

const emit = defineEmits<{
  click: [tank: Tank]
}>()

const getStatusColor = (status: Tank['status']) => {
  switch (status) {
    case 'full': return 'text-blue-500 bg-blue-50 border-blue-200'
    case 'normal': return 'text-green-500 bg-green-50 border-green-200'
    case 'low': return 'text-yellow-500 bg-yellow-50 border-yellow-200'
    case 'critical': return 'text-red-500 bg-red-50 border-red-200'
    case 'empty': return 'text-gray-500 bg-gray-50 border-gray-200'
    default: return 'text-gray-500 bg-gray-50 border-gray-200'
  }
}

const getStatusIcon = (status: Tank['status']) => {
  switch (status) {
    case 'full': return 'lucide:droplets'
    case 'normal': return 'lucide:droplets'
    case 'low': return 'lucide:alert-triangle'
    case 'critical': return 'lucide:alert-circle'
    case 'empty': return 'lucide:circle'
    default: return 'lucide:help-circle'
  }
}

const getLevelBarColor = (level: number) => {
  if (level >= 90) return 'bg-blue-500'
  if (level >= 60) return 'bg-green-500'
  if (level >= 25) return 'bg-yellow-500'
  if (level > 0) return 'bg-red-500'
  return 'bg-gray-300'
}

const formatTimeAgo = (date: Date) => {
  const now = new Date()
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))
  
  if (diffInMinutes < 60) {
    return `${diffInMinutes}m ago`
  } else if (diffInMinutes < 1440) {
    return `${Math.floor(diffInMinutes / 60)}h ago`
  } else {
    return `${Math.floor(diffInMinutes / 1440)}d ago`
  }
}
</script>

<template>
  <div 
    @click="emit('click', tank)"
    :class="[
      'relative overflow-hidden rounded-xl border-2 p-4 cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-[1.02]',
      getStatusColor(tank.status)
    ]"
  >
    <!-- Status Badge -->
    <div class="absolute top-3 right-3">
      <div :class="['w-3 h-3 rounded-full', tank.isOnline ? 'bg-green-500' : 'bg-red-500']"></div>
    </div>

    <!-- Header -->
    <div class="flex items-start justify-between mb-3">
      <div class="flex items-center space-x-3">
        <div :class="['w-10 h-10 rounded-full flex items-center justify-center', getStatusColor(tank.status)]">
          <Icon :name="getStatusIcon(tank.status)" class="w-5 h-5" />
        </div>
        <div>
          <h3 class="font-semibold text-gray-900 dark:text-white">{{ tank.name }}</h3>
          <p class="text-sm text-gray-500 dark:text-gray-400">{{ tank.location }}</p>
        </div>
      </div>
    </div>

    <!-- Level Display -->
    <div class="mb-4">
      <div class="flex items-center justify-between mb-2">
        <span class="text-2xl font-bold text-gray-900 dark:text-white">{{ Math.round(tank.currentLevel) }}%</span>
        <span class="text-sm font-medium text-gray-600 dark:text-gray-300 capitalize">{{ tank.status }}</span>
      </div>
      
      <!-- Level Bar -->
      <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-2">
        <div 
          :class="['h-2 rounded-full transition-all duration-300', getLevelBarColor(tank.currentLevel)]"
          :style="{ width: `${tank.currentLevel}%` }"
        ></div>
      </div>
      
      <!-- Volume Info -->
      <div class="flex justify-between text-sm text-gray-600 dark:text-gray-400">
        <span>{{ Math.round(tank.currentLevel / 100 * tank.volume) }}L</span>
        <span>{{ tank.volume }}L</span>
      </div>
    </div>

    <!-- Additional Details -->
    <div v-if="showDetails" class="space-y-2 text-sm">
      <div class="flex justify-between">
        <span class="text-gray-500 dark:text-gray-400">Brand:</span>
        <span class="text-gray-900 dark:text-white">{{ tank.brand }}</span>
      </div>
      <div class="flex justify-between">
        <span class="text-gray-500 dark:text-gray-400">Sensor:</span>
        <span class="text-gray-900 dark:text-white">{{ tank.sensor.type }}</span>
      </div>
    </div>

    <!-- Last Updated -->
    <div class="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600">
      <p class="text-xs text-gray-500 dark:text-gray-400">
        Updated {{ formatTimeAgo(tank.lastUpdated) }}
      </p>
    </div>
  </div>
</template>
