<script setup lang="ts">
import type { ConnectedDevice } from '~/stores/tanks/types'

interface Props {
  devices: ConnectedDevice[]
}

const props = defineProps<Props>()

const emit = defineEmits<{
  addSensor: []
  manageSensors: []
}>()
</script>

<template>
  <div class="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 mb-20">
    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Connected Sensors</h3>
        <button 
          @click="emit('manageSensors')"
          class="text-sm text-blue-500 hover:text-blue-600 font-medium"
        >
          Manage
        </button>
      </div>
    </div>
    
    <div class="p-6">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div 
          v-for="device in devices" 
          :key="device.name"
          class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors"
        >
          <div class="flex items-center space-x-3 mb-3">
            <div class="w-8 h-8 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
              <Icon :name="`lucide:${device.icon}`" class="w-4 h-4 text-gray-600 dark:text-gray-300" />
            </div>
            <div>
              <h4 class="font-medium text-gray-900 dark:text-white">{{ device.name }}</h4>
            </div>
          </div>
          <div class="space-y-1">
            <div class="flex justify-between text-sm">
              <span class="text-gray-500 dark:text-gray-400">Active:</span>
              <span class="text-gray-900 dark:text-white">{{ device.duration }}</span>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-500 dark:text-gray-400">Power:</span>
              <span class="text-gray-900 dark:text-white">{{ device.usage }}</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Add Sensor Button -->
      <div class="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
        <button 
          @click="emit('addSensor')"
          class="flex items-center space-x-2 px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors"
        >
          <Icon name="lucide:plus" class="w-4 h-4" />
          <span class="text-sm font-medium">Add Sensor</span>
        </button>
      </div>
    </div>
  </div>
</template>
