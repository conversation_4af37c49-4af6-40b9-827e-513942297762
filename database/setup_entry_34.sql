-- Setup for Entry ID 34 with level column
-- Run this in your Supabase SQL editor

-- First, ensure the device_metrics table has the level column
ALTER TABLE device_metrics ADD COLUMN IF NOT EXISTS level DECIMAL(5,2);

-- Insert or update entry with ID 34
INSERT INTO device_metrics (id, device_id, level, timestamp, created_at, updated_at)
VALUES (
  34,
  'wasl-main-tank',
  75.0,  -- Initial level at 75%
  NOW(),
  NOW(),
  NOW()
)
ON CONFLICT (id) DO UPDATE SET
  device_id = EXCLUDED.device_id,
  level = EXCLUDED.level,
  timestamp = EXCLUDED.timestamp,
  updated_at = EXCLUDED.updated_at;

-- Verify the entry exists
SELECT * FROM device_metrics WHERE id = 34;

-- Test update query (you can run this to test real-time updates)
-- UPDATE device_metrics SET level = 85.5, updated_at = NOW() WHERE id = 34;
