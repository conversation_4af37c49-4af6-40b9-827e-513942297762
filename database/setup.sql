-- Create device_metrics table for storing tank height data
CREATE TABLE IF NOT EXISTS device_metrics (
  id BIGSERIAL PRIMARY KEY,
  device_id TEXT NOT NULL,
  tank_height DECIMAL(5,2) NOT NULL, -- Height in meters with 2 decimal places
  timestamp TIMESTAMPTZ DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create index for faster queries on device_id
CREATE INDEX IF NOT EXISTS idx_device_metrics_device_id ON device_metrics(device_id);

-- Create index for timestamp queries
CREATE INDEX IF NOT EXISTS idx_device_metrics_timestamp ON device_metrics(timestamp DESC);

-- Enable Row Level Security (optional)
ALTER TABLE device_metrics ENABLE ROW LEVEL SECURITY;

-- Create a policy to allow all operations (adjust as needed for your security requirements)
CREATE POLICY IF NOT EXISTS "Allow all operations on device_metrics" ON device_metrics
  FOR ALL USING (true);

-- Insert sample data for WASL Main Tank
INSERT INTO device_metrics (device_id, tank_height, timestamp) VALUES
  ('wasl-main-tank', 1.85, NOW() - INTERVAL '5 minutes'),
  ('wasl-main-tank', 1.90, NOW() - INTERVAL '3 minutes'),
  ('wasl-main-tank', 1.88, NOW() - INTERVAL '1 minute'),
  ('wasl-main-tank', 1.92, NOW())
ON CONFLICT DO NOTHING;

-- Create a function to automatically update the updated_at column
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at
DROP TRIGGER IF EXISTS update_device_metrics_updated_at ON device_metrics;
CREATE TRIGGER update_device_metrics_updated_at
    BEFORE UPDATE ON device_metrics
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
