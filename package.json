{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@nuxt/eslint": "^1.4.0", "@nuxt/fonts": "^0.11.4", "@nuxt/icon": "^1.13.0", "@nuxt/image": "^1.10.0", "@photo-sphere-viewer/markers-plugin": "^5.13.2", "@pinia/nuxt": "^0.11.0", "@supabase/supabase-js": "^2.52.0", "@tailwindcss/vite": "^4.1.7", "@vueuse/core": "^13.3.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "embla-carousel-vue": "^8.6.0", "eslint": "^9.27.0", "lucide-vue-next": "^0.511.0", "luxon": "^3.7.1", "mapbox-gl": "^3.12.0", "motion-v": "^1.5.0", "nuxt": "^3.17.3", "photo-sphere-viewer": "^4.8.1", "pinia": "^3.0.2", "reka-ui": "^2.3.0", "shadcn-nuxt": "^2.1.0", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.7", "tw-animate-css": "^1.3.0", "vue": "^3.5.13", "vue-router": "^4.5.1"}, "devDependencies": {"@iconify-json/lucide": "^1.2.57", "@types/luxon": "^3.7.1"}}