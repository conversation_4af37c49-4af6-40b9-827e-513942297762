# Supabase Real-time Integration

This document explains how the WataBot Admin Dashboard integrates with Supabase for real-time tank monitoring.

## Overview

The application now connects to Supabase to receive real-time updates from the `device_metrics` table, specifically monitoring the `tank_height` column for the WASL Main Tank.

## Database Setup

### 1. Create the Table

Run the SQL script in `database/setup.sql` in your Supabase SQL editor to create the required table structure:

```sql
-- The script creates:
-- - device_metrics table with proper indexes
-- - Row Level Security policies
-- - Sample data for testing
-- - Automatic updated_at trigger
```

### 2. Table Structure

```sql
device_metrics (
  id: BIGSERIAL PRIMARY KEY,
  device_id: TEXT NOT NULL,           -- 'wasl-main-tank' for WASL Main Tank
  tank_height: DECIMAL(5,2) NOT NULL, -- Height in meters (e.g., 1.85)
  timestamp: TIMESTAMPTZ,             -- When the reading was taken
  created_at: TIMESTAMPTZ,            -- Record creation time
  updated_at: TIMESTAMPTZ             -- Record update time
)
```

## How It Works

### 1. Real-time Subscription

The application subscribes to changes in the `device_metrics` table:

```typescript
supabase
  .channel('device_metrics_changes')
  .on('postgres_changes', {
    event: '*',
    schema: 'public',
    table: 'device_metrics',
    filter: `device_id=eq.wasl-main-tank`
  }, (payload) => {
    // Update tank data in real-time
  })
```

### 2. Data Conversion

Tank height from the database is converted to:
- **Water Level Percentage**: `(tank_height / max_height) * 100`
- **Volume**: `(percentage / 100) * tank_volume`
- **Status**: Based on percentage (full, normal, low, critical, empty)

### 3. Tank Configuration

```typescript
WASL_TANK_CONFIG = {
  device_id: 'wasl-main-tank',
  tank_id: 101,
  max_height: 2.5,  // meters
  volume: 2000,     // liters
  name: 'WASL Main Tank'
}
```

## Testing the Integration

### 1. Using the Test Component

The application includes a `SupabaseStatus` component that shows:
- Connection status to Supabase
- Device ID being monitored
- Button to insert test data

### 2. Manual Testing

Insert test data directly in Supabase:

```sql
INSERT INTO device_metrics (device_id, tank_height, timestamp) 
VALUES ('wasl-main-tank', 2.1, NOW());
```

### 3. Expected Behavior

When new data is inserted or updated:
1. Real-time subscription receives the change
2. Tank level percentage is calculated
3. Tank status is updated (full/normal/low/critical/empty)
4. UI components update automatically
5. Activity feed shows the change

## Environment Variables

The application uses these environment variables:

```env
SUPABASE_URL=https://xwkrufpcgaxkmhqqvjyv.supabase.co
SUPABASE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## File Structure

```
lib/
├── supabase.ts              # Supabase client and utilities

stores/tanks/
├── tanks.store.ts           # Updated with Supabase integration
└── types/index.ts           # Tank and device metric types

components/
├── SupabaseStatus.vue       # Connection status and testing
├── TankStatusCard.vue       # Shows real-time tank data
└── ActivityFeed.vue         # Shows real-time activities

database/
└── setup.sql               # Database setup script
```

## Key Features

### Real-time Updates
- Automatic UI updates when tank height changes
- No page refresh required
- Live activity feed

### Error Handling
- Connection status monitoring
- Graceful fallback to mock data
- Error messages in UI

### Data Validation
- Tank height validation (0 to max_height)
- Status calculation based on levels
- Timestamp handling

## Troubleshooting

### Connection Issues
1. Check Supabase URL and key in environment variables
2. Verify table exists and has correct structure
3. Check Row Level Security policies
4. Monitor browser console for errors

### No Real-time Updates
1. Verify real-time is enabled in Supabase project
2. Check subscription status in browser console
3. Ensure device_id matches exactly ('wasl-main-tank')
4. Test with manual data insertion

### Data Not Showing
1. Check if initial data fetch is working
2. Verify tank configuration matches database
3. Check for JavaScript errors in console
4. Ensure proper data types (DECIMAL for tank_height)

## Next Steps

1. **Production Setup**: Update environment variables for production
2. **Security**: Implement proper Row Level Security policies
3. **Multiple Tanks**: Extend to support multiple tank devices
4. **Historical Data**: Add charts showing historical tank levels
5. **Alerts**: Implement real-time alerts for critical levels
