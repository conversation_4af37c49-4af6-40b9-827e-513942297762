import { DateTime } from 'luxon'

export const useRelativeTime = () => {
  /**
   * Format a date to a human-readable relative time
   * @param date - The date to format
   * @param options - Formatting options
   * @returns Human-readable relative time string
   */
  const formatRelativeTime = (
    date: Date | string | null | undefined,
    options: {
      style?: 'long' | 'short' | 'narrow'
      numeric?: 'always' | 'auto'
      updateInterval?: boolean
    } = {}
  ): string => {
    if (!date) return 'Never'

    const {
      style = 'short',
      numeric = 'auto',
      updateInterval = false
    } = options

    try {
      const dateTime = DateTime.fromJSDate(new Date(date))
      const now = DateTime.now()
      
      if (!dateTime.isValid) {
        return 'Invalid date'
      }

      // Calculate the difference
      const diff = now.diff(dateTime, ['years', 'months', 'days', 'hours', 'minutes', 'seconds'])
      
      // Custom formatting for better UX
      const years = Math.floor(diff.years)
      const months = Math.floor(diff.months)
      const days = Math.floor(diff.days)
      const hours = Math.floor(diff.hours)
      const minutes = Math.floor(diff.minutes)
      const seconds = Math.floor(diff.seconds)

      // Handle future dates
      if (seconds < 0) {
        return 'Just now'
      }

      // Format based on time difference
      if (years > 0) {
        return years === 1 ? 'Last year' : `${years} years ago`
      }
      
      if (months > 0) {
        if (months === 1) {
          return 'Last month'
        } else if (months < 12) {
          return `${months} months ago`
        }
      }
      
      if (days > 0) {
        if (days === 1) {
          return 'Yesterday'
        } else if (days === 7) {
          return 'Last week'
        } else if (days < 7) {
          return `${days} days ago`
        } else if (days < 14) {
          return 'Last week'
        } else if (days < 30) {
          const weeks = Math.floor(days / 7)
          return `${weeks} weeks ago`
        }
      }
      
      if (hours > 0) {
        if (hours === 1) {
          return '1 hour ago'
        } else if (hours < 24) {
          return `${hours} hours ago`
        }
      }
      
      if (minutes > 0) {
        if (minutes === 1) {
          return '1 min ago'
        } else if (minutes < 60) {
          return `${minutes} mins ago`
        }
      }
      
      if (seconds < 10) {
        return 'Just now'
      } else if (seconds < 60) {
        return `${seconds} secs ago`
      }

      return 'Just now'
      
    } catch (error) {
      console.error('Error formatting relative time:', error)
      return 'Unknown'
    }
  }

  /**
   * Format a date to a precise relative time using Luxon's built-in formatter
   * @param date - The date to format
   * @returns Luxon's relative time string
   */
  const formatPreciseRelativeTime = (date: Date | string | null | undefined): string => {
    if (!date) return 'Never'

    try {
      const dateTime = DateTime.fromJSDate(new Date(date))
      if (!dateTime.isValid) {
        return 'Invalid date'
      }

      return dateTime.toRelative({ style: 'short' }) || 'Unknown'
    } catch (error) {
      console.error('Error formatting precise relative time:', error)
      return 'Unknown'
    }
  }

  /**
   * Format a date to show both relative time and absolute time
   * @param date - The date to format
   * @returns Combined relative and absolute time string
   */
  const formatDetailedTime = (date: Date | string | null | undefined): {
    relative: string
    absolute: string
    iso: string
  } => {
    if (!date) {
      return {
        relative: 'Never',
        absolute: 'Never',
        iso: ''
      }
    }

    try {
      const dateTime = DateTime.fromJSDate(new Date(date))
      if (!dateTime.isValid) {
        return {
          relative: 'Invalid date',
          absolute: 'Invalid date',
          iso: ''
        }
      }

      return {
        relative: formatRelativeTime(date),
        absolute: dateTime.toLocaleString(DateTime.DATETIME_MED),
        iso: dateTime.toISO() || ''
      }
    } catch (error) {
      console.error('Error formatting detailed time:', error)
      return {
        relative: 'Unknown',
        absolute: 'Unknown',
        iso: ''
      }
    }
  }

  /**
   * Create a reactive relative time that updates automatically
   * @param date - The date to track
   * @param intervalMs - Update interval in milliseconds (default: 60000 = 1 minute)
   * @returns Reactive relative time string
   */
  const useReactiveRelativeTime = (
    date: Ref<Date | string | null | undefined>,
    intervalMs: number = 60000
  ) => {
    const relativeTime = ref(formatRelativeTime(date.value))
    
    let interval: NodeJS.Timeout | null = null

    const updateRelativeTime = () => {
      relativeTime.value = formatRelativeTime(date.value)
    }

    const startUpdating = () => {
      updateRelativeTime()
      interval = setInterval(updateRelativeTime, intervalMs)
    }

    const stopUpdating = () => {
      if (interval) {
        clearInterval(interval)
        interval = null
      }
    }

    // Watch for date changes
    watch(date, updateRelativeTime, { immediate: true })

    // Auto-start updating
    onMounted(startUpdating)
    onUnmounted(stopUpdating)

    return {
      relativeTime: readonly(relativeTime),
      updateRelativeTime,
      startUpdating,
      stopUpdating
    }
  }

  return {
    formatRelativeTime,
    formatPreciseRelativeTime,
    formatDetailedTime,
    useReactiveRelativeTime
  }
}
