import { createClient } from '@supabase/supabase-js'

export const useSupabase = () => {
  const config = useRuntimeConfig()
  
  const supabaseUrl = config.public.supabaseUrl || 'https://xwkrufpcgaxkmhqqvjyv.supabase.co'
  const supabaseKey = config.public.supabaseKey || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inh3a3J1ZnBjZ2F4a21ocXF2anl2Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczMTMzMzM1NywiZXhwIjoyMDQ2OTA5MzU3fQ.lktmhYK4EAiPjEWat-p2yhfBlAkAHqNUTf10WwDIjck'
  
  const supabase = createClient(supabaseUrl, supabaseKey)
  
  return { supabase }
}
