<script setup lang="ts">
import { ref, computed } from 'vue'
import type { Location, Tank, DeviceStatus, RecentActivity } from '~/types'

// Mock data for water level monitoring locations
const locations = ref<Location[]>([
  {
    id: 1,
    name: 'Rooftop',
    description: 'Main building rooftop water storage',
    icon: 'building',
    tanks: [
      {
        id: 101,
        name: 'Main Water Tank',
        brand: 'Sintex',
        height: 2.5,
        volume: 1000,
        currentLevel: 75,
        status: 'normal',
        lastUpdated: new Date(Date.now() - 5 * 60000),
        isOnline: true,
        sensorId: 'SNS-001'
      },
      {
        id: 102,
        name: 'Backup Tank',
        brand: 'Sintex',
        height: 2.0,
        volume: 800,
        currentLevel: 60,
        status: 'normal',
        lastUpdated: new Date(Date.now() - 3 * 60000),
        isOnline: true,
        sensorId: 'SNS-002'
      }
    ]
  },
  {
    id: 2,
    name: 'Basement',
    description: 'Underground water storage facility',
    icon: 'home',
    tanks: [
      {
        id: 201,
        name: 'Reserve Tank',
        brand: 'Penguin',
        height: 1.8,
        volume: 500,
        currentLevel: 45,
        status: 'low',
        lastUpdated: new Date(Date.now() - 15 * 60000),
        isOnline: true,
        sensorId: 'SNS-003'
      }
    ]
  },
  {
    id: 3,
    name: 'Backyard',
    description: 'Garden and irrigation water storage',
    icon: 'trees',
    tanks: [
      {
        id: 301,
        name: 'Garden Tank',
        brand: 'Supreme',
        height: 1.5,
        volume: 300,
        currentLevel: 90,
        status: 'normal',
        lastUpdated: new Date(Date.now() - 2 * 60000),
        isOnline: false,
        sensorId: 'SNS-004'
      }
    ]
  },
  {
    id: 4,
    name: 'Utility Room',
    description: 'Emergency and utility water storage',
    icon: 'wrench',
    tanks: [
      {
        id: 401,
        name: 'Emergency Tank',
        brand: 'Kaveri',
        height: 1.2,
        volume: 200,
        currentLevel: 20,
        status: 'critical',
        lastUpdated: new Date(Date.now() - 30 * 60000),
        isOnline: true,
        sensorId: 'SNS-005'
      }
    ]
  }
])

// Recent activity data
const recentActivity = ref<RecentActivity[]>([
  {
    id: 1,
    tankName: 'Garden Tank',
    activity: 'Tank reached full capacity',
    time: new Date(Date.now() - 2 * 60 * 60000),
    type: 'success',
    icon: 'check-circle'
  },
  {
    id: 2,
    tankName: 'Reserve Tank',
    activity: 'Water level is low (45%)',
    time: new Date(Date.now() - 4 * 60 * 60000),
    type: 'warning',
    icon: 'alert-triangle'
  },
  {
    id: 3,
    tankName: 'Emergency Tank',
    activity: 'Critical water level detected',
    time: new Date(Date.now() - 6 * 60 * 60000),
    type: 'error',
    icon: 'alert-circle'
  },
  {
    id: 4,
    tankName: 'Main Water Tank',
    activity: 'Sensor reconnected successfully',
    time: new Date(Date.now() - 8 * 60 * 60000),
    type: 'info',
    icon: 'wifi'
  }
])

// User and organization data
const user = ref({
  name: 'Admin',
  avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face&auto=format'
})

const currentOrganization = ref({
  name: 'Acme Manufacturing Ltd',
  logo: 'https://images.unsplash.com/photo-1560179707-f14e90ef3623?w=40&h=40&fit=crop&auto=format',
  plan: 'Enterprise',
  totalTanks: 4,
  activeSensors: 7
})

// Flatten all tanks from all locations
const allTanks = computed(() => locations.value.flatMap(location => location.tanks))

const totalCapacity = computed(() => allTanks.value.reduce((sum, tank) => sum + tank.volume, 0))
const totalCurrentLevel = computed(() => allTanks.value.reduce((sum, tank) => sum + (tank.currentLevel / 100 * tank.volume), 0))
const overallPercentage = computed(() => Math.round((totalCurrentLevel.value / totalCapacity.value) * 100))

// Helper to get location status based on tanks
const getLocationStatus = (location: Location) => {
  const tanks = location.tanks
  if (tanks.some(tank => tank.status === 'critical')) return 'critical'
  if (tanks.some(tank => tank.status === 'low')) return 'low'
  return 'normal'
}

// Helper to check if location has online tanks
const isLocationOnline = (location: Location) => {
  return location.tanks.some(tank => tank.isOnline)
}

// Helper to get tank count for location
const getTankCount = (location: Location) => {
  return location.tanks.length
}

// Helper to get average level for location
const getLocationAverageLevel = (location: Location) => {
  const onlineTanks = location.tanks.filter(tank => tank.isOnline)
  if (onlineTanks.length === 0) return 0
  return Math.round(onlineTanks.reduce((sum, tank) => sum + tank.currentLevel, 0) / onlineTanks.length)
}

// Helper to get total volume for location
const getLocationTotalVolume = (location: Location) => {
  return location.tanks.reduce((sum, tank) => sum + tank.volume, 0)
}

// Helper to get current volume for location
const getLocationCurrentVolume = (location: Location) => {
  return Math.round(location.tanks.reduce((sum, tank) => sum + (tank.currentLevel / 100 * tank.volume), 0))
}

// Helper to get most recent update for location
const getLocationLastUpdated = (location: Location) => {
  const dates = location.tanks.map(tank => tank.lastUpdated)
  return new Date(Math.max(...dates.map(date => date.getTime())))
}

// Helper functions
const formatTimeAgo = (date: Date) => {
  const now = new Date()
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))

  if (diffInMinutes < 60) {
    return `${diffInMinutes}m ago`
  } else if (diffInMinutes < 1440) {
    return `${Math.floor(diffInMinutes / 60)}h ago`
  } else {
    return `${Math.floor(diffInMinutes / 1440)}d ago`
  }
}

const getActivityColor = (type: string) => {
  switch (type) {
    case 'success': return 'text-green-500'
    case 'warning': return 'text-yellow-500'
    case 'error': return 'text-red-500'
    case 'info': return 'text-blue-500'
    default: return 'text-gray-500'
  }
}

const getActivityBgColor = (type: string) => {
  switch (type) {
    case 'success': return 'bg-green-100 dark:bg-green-900/20'
    case 'warning': return 'bg-yellow-100 dark:bg-yellow-900/20'
    case 'error': return 'bg-red-100 dark:bg-red-900/20'
    case 'info': return 'bg-blue-100 dark:bg-blue-900/20'
    default: return 'bg-gray-100 dark:bg-gray-900/20'
  }
}

const getStatusColor = (status: DeviceStatus) => {
  switch (status) {
    case 'normal': return 'text-green-500'
    case 'low': return 'text-yellow-500'
    case 'critical': return 'text-red-500'
    default: return 'text-gray-500'
  }
}

const navigateToDevice = (locationId: number) => {
  navigateTo(`/location/${locationId}`)
}
</script>

<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Header -->
    <div class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between h-16">
          <div class="flex items-center space-x-4">
            <div class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
              <Icon name="lucide:droplets" class="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 class="text-xl font-semibold text-gray-900 dark:text-white">WataBot</h1>
              <p class="text-sm text-gray-500 dark:text-gray-400">{{ currentOrganization.name }} • {{ currentOrganization.plan }}</p>
            </div>
          </div>
          <div class="flex items-center space-x-4">
            <div class="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-300">
              <Icon name="lucide:building" class="w-4 h-4" />
              <span>{{ currentOrganization.activeSensors }} sensors</span>
            </div>
            <Icon name="lucide:bell" class="w-5 h-5 text-gray-600 dark:text-gray-300" />
            <div class="w-8 h-8 rounded-full overflow-hidden">
              <img :src="user.avatar" :alt="user.name" class="w-full h-full object-cover" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Greeting Section -->
      <div class="mb-8">
        <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          Good {{ new Date().getHours() < 12 ? 'Morning' : new Date().getHours() < 18 ? 'Afternoon' : 'Evening' }}, {{ user.name }}
        </h2>
        <p class="text-gray-600 dark:text-gray-400">Monitor your water levels and manage your tanks efficiently</p>
      </div>

      <!-- Location Tabs -->
      <div class="flex space-x-1 mb-8">
        <button class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
          <Icon name="lucide:plus" class="w-4 h-4 mr-2 inline" />
          Add Device
        </button>
        <button class="px-4 py-2 text-sm font-medium text-white bg-blue-500 rounded-lg">
          All Locations
        </button>
        <button class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg">
          Rooftop
        </button>
        <button class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg">
          Basement
        </button>
        <button class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg">
          Backyard
        </button>
      </div>

      <!-- Location Cards Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div
          v-for="location in locations"
          :key="location.id"
          @click="navigateToDevice(location.id)"
          class="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-all duration-200"
        >
          <div class="flex items-center justify-between mb-4">
            <div class="relative">
              <div class="w-12 h-12 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
                <Icon :name="`lucide:${location.icon}`" class="w-6 h-6 text-blue-500" />
              </div>
              <div
                :class="['absolute -top-1 -right-1 w-4 h-4 rounded-full border-2 border-white dark:border-gray-800', isLocationOnline(location) ? 'bg-green-500' : 'bg-red-500']"
              ></div>
            </div>
            <div class="text-right">
              <div :class="['w-2 h-2 rounded-full mb-1 ml-auto', getStatusColor(getLocationStatus(location)).replace('text-', 'bg-')]"></div>
              <span class="text-xs text-gray-500 dark:text-gray-400 uppercase">{{ isLocationOnline(location) ? 'ONLINE' : 'OFFLINE' }}</span>
            </div>
          </div>

          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-1">{{ location.name }}</h3>
          <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">{{ location.description }} • {{ getTankCount(location) }} Tank{{ getTankCount(location) > 1 ? 's' : '' }}</p>

          <div class="space-y-3">
            <div class="flex justify-between items-center">
              <span class="text-2xl font-bold text-gray-900 dark:text-white">{{ getLocationAverageLevel(location) }}%</span>
              <span class="text-sm text-gray-500 dark:text-gray-400">{{ getLocationCurrentVolume(location) }}L</span>
            </div>
            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div
                :class="['h-2 rounded-full transition-all duration-500', getStatusColor(getLocationStatus(location)).replace('text-', 'bg-')]"
                :style="{ width: `${getLocationAverageLevel(location)}%` }"
              ></div>
            </div>
            <div class="flex justify-between items-center text-sm text-gray-500 dark:text-gray-400">
              <span>Last updated {{ formatTimeAgo(getLocationLastUpdated(location)) }}</span>
              <span>{{ getLocationTotalVolume(location) }}L capacity</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Overall Water Storage Status -->
      <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-2xl p-6 text-white mb-8">
        <div class="flex items-center justify-between">
          <div>
            <h2 class="text-lg font-medium mb-2">Total Water Storage</h2>
            <div class="flex items-center space-x-4">
              <div class="flex items-center space-x-2">
                <Icon name="lucide:droplets" class="w-5 h-5" />
                <span class="text-3xl font-bold">{{ Math.round(totalCurrentLevel) }}L</span>
              </div>
              <div class="text-sm opacity-90">
                <p>{{ overallPercentage }}% of {{ totalCapacity }}L capacity</p>
                <p>{{ locations.filter(l => l.isOnline).length }} of {{ locations.length }} tanks online</p>
              </div>
            </div>
          </div>
          <div class="w-24 h-24 relative">
            <svg class="w-24 h-24 transform -rotate-90" viewBox="0 0 100 100">
              <circle cx="50" cy="50" r="40" stroke="rgba(255,255,255,0.2)" stroke-width="8" fill="none" />
              <circle
                cx="50"
                cy="50"
                r="40"
                stroke="white"
                stroke-width="8"
                fill="none"
                :stroke-dasharray="`${overallPercentage * 2.51} 251`"
                class="transition-all duration-500"
              />
            </svg>
            <div class="absolute inset-0 flex items-center justify-center">
              <Icon name="lucide:gauge" class="w-8 h-8 text-white" />
            </div>
          </div>
        </div>
      </div>

      <!-- Recent Activity -->
      <div class="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 mb-20">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Recent Activity</h3>
            <button class="text-sm text-blue-500 hover:text-blue-600">See all</button>
          </div>
        </div>

        <div class="divide-y divide-gray-200 dark:divide-gray-700">
          <div
            v-for="activity in recentActivity"
            :key="activity.id"
            class="p-6 flex items-center justify-between hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors"
          >
            <div class="flex items-center space-x-4">
              <div :class="['w-10 h-10 rounded-full flex items-center justify-center', getActivityBgColor(activity.type)]">
                <Icon :name="`lucide:${activity.icon}`" :class="['w-5 h-5', getActivityColor(activity.type)]" />
              </div>
              <div>
                <h4 class="font-medium text-gray-900 dark:text-white">{{ activity.tankName }}</h4>
                <p class="text-sm text-gray-500 dark:text-gray-400">{{ activity.activity }}</p>
              </div>
            </div>
            <div class="text-right">
              <p class="text-sm text-gray-500 dark:text-gray-400">{{ formatTimeAgo(activity.time) }}</p>
            </div>
          </div>
        </div>

        <!-- Add Device Button -->
        <div class="p-6 border-t border-gray-200 dark:border-gray-700">
          <button class="w-12 h-12 bg-blue-500 hover:bg-blue-600 rounded-full flex items-center justify-center text-white transition-colors">
            <Icon name="lucide:plus" class="w-6 h-6" />
          </button>
        </div>
      </div>
    </div>

    <!-- Bottom Navigation -->
    <div class="fixed bottom-0 left-0 right-0 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 px-4 py-2">
      <div class="max-w-7xl mx-auto">
        <div class="flex items-center justify-around">
          <button class="flex flex-col items-center space-y-1 p-2 text-blue-500">
            <Icon name="lucide:home" class="w-6 h-6" />
            <span class="text-xs">Home</span>
          </button>
          <button class="flex flex-col items-center space-y-1 p-2 text-gray-400 dark:text-gray-500">
            <Icon name="lucide:grid-3x3" class="w-6 h-6" />
            <span class="text-xs">Tanks</span>
          </button>
          <button class="flex flex-col items-center space-y-1 p-2 text-gray-400 dark:text-gray-500">
            <Icon name="lucide:bar-chart-3" class="w-6 h-6" />
            <span class="text-xs">Analytics</span>
          </button>
          <button class="flex flex-col items-center space-y-1 p-2 text-gray-400 dark:text-gray-500">
            <Icon name="lucide:user" class="w-6 h-6" />
            <span class="text-xs">Profile</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>