<script setup lang="ts">
import { ref, computed } from 'vue'
import type { WaterDevice, DeviceStatus } from '~/types'

// Mock data for water level devices
const devices = ref<WaterDevice[]>([
  {
    id: 1,
    name: 'Main Tank',
    location: 'Rooftop',
    currentLevel: 75,
    maxCapacity: 1000,
    status: 'normal',
    lastUpdated: new Date(),
    icon: 'droplets',
    isOnline: true,
    deviceCount: 2
  },
  {
    id: 2,
    name: 'Reserve Tank',
    location: 'Basement',
    currentLevel: 45,
    maxCapacity: 500,
    status: 'low',
    lastUpdated: new Date(),
    icon: 'droplets',
    isOnline: true,
    deviceCount: 2
  },
  {
    id: 3,
    name: 'Garden Tank',
    location: 'Backyard',
    currentLevel: 90,
    maxCapacity: 300,
    status: 'normal',
    lastUpdated: new Date(),
    icon: 'droplets',
    isOnline: false,
    deviceCount: 1
  },
  {
    id: 4,
    name: 'Emergency Tank',
    location: 'Utility Room',
    currentLevel: 20,
    maxCapacity: 200,
    status: 'critical',
    lastUpdated: new Date(),
    icon: 'droplets',
    isOnline: true,
    deviceCount: 2
  }
])

const totalCapacity = computed(() => devices.value.reduce((sum, device) => sum + device.maxCapacity, 0))
const totalCurrentLevel = computed(() => devices.value.reduce((sum, device) => sum + (device.currentLevel / 100 * device.maxCapacity), 0))
const overallPercentage = computed(() => Math.round((totalCurrentLevel.value / totalCapacity.value) * 100))

const getStatusColor = (status: DeviceStatus) => {
  switch (status) {
    case 'normal': return 'text-green-500'
    case 'low': return 'text-yellow-500'
    case 'critical': return 'text-red-500'
    default: return 'text-gray-500'
  }
}



const navigateToDevice = (deviceId: number) => {
  navigateTo(`/device/${deviceId}`)
}
</script>

<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Header -->
    <div class="bg-white dark:bg-gray-800 shadow-sm">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between h-16">
          <div class="flex items-center space-x-4">
            <div class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
              <Icon name="lucide:droplets" class="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 class="text-xl font-semibold text-gray-900 dark:text-white">WataBot</h1>
              <p class="text-sm text-gray-500 dark:text-gray-400">Admin Dashboard</p>
            </div>
          </div>
          <div class="flex items-center space-x-4">
            <div class="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-300">
              <Icon name="lucide:battery" class="w-4 h-4" />
              <span>85%</span>
            </div>
            <div class="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-300">
              <Icon name="lucide:wifi" class="w-4 h-4" />
              <span>Connected</span>
            </div>
            <Icon name="lucide:bell" class="w-5 h-5 text-gray-600 dark:text-gray-300" />
          </div>
        </div>
      </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Location Tabs -->
      <div class="flex space-x-1 mb-8">
        <button class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <Icon name="lucide:plus" class="w-4 h-4 mr-2 inline" />
          Add Device
        </button>
        <button class="px-4 py-2 text-sm font-medium text-white bg-blue-500 rounded-lg shadow-sm">
          All Locations
        </button>
        <button class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg">
          Rooftop
        </button>
        <button class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg">
          Basement
        </button>
        <button class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg">
          Backyard
        </button>
      </div>

      <!-- Device Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div
          v-for="device in devices"
          :key="device.id"
          @click="navigateToDevice(device.id)"
          class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 cursor-pointer hover:shadow-md transition-all duration-200 hover:scale-105"
        >
          <div class="flex items-center justify-between mb-4">
            <div class="relative">
              <div class="w-12 h-12 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
                <Icon :name="`lucide:${device.icon}`" class="w-6 h-6 text-blue-500" />
              </div>
              <div
                :class="['absolute -top-1 -right-1 w-4 h-4 rounded-full border-2 border-white dark:border-gray-800', device.isOnline ? 'bg-green-500' : 'bg-red-500']"
              ></div>
            </div>
            <div class="text-right">
              <div :class="['w-2 h-2 rounded-full mb-1 ml-auto', getStatusColor(device.status).replace('text-', 'bg-')]"></div>
              <span class="text-xs text-gray-500 dark:text-gray-400 uppercase">{{ device.isOnline ? 'ON' : 'OFF' }}</span>
            </div>
          </div>

          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-1">{{ device.name }}</h3>
          <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">{{ Math.round(device.currentLevel / 100 * device.maxCapacity) }}L | {{ device.deviceCount }} Devices</p>

          <div class="space-y-3">
            <div class="flex justify-between items-center">
              <span class="text-2xl font-bold text-gray-900 dark:text-white">{{ device.currentLevel }}%</span>
              <span class="text-sm text-gray-500 dark:text-gray-400">{{ device.maxCapacity }}L</span>
            </div>
            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div
                :class="['h-2 rounded-full transition-all duration-500', getStatusColor(device.status).replace('text-', 'bg-')]"
                :style="{ width: `${device.currentLevel}%` }"
              ></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Overall Water Status -->
      <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-2xl p-6 text-white mb-8">
        <div class="flex items-center justify-between">
          <div>
            <h2 class="text-lg font-medium mb-2">Water Conservation</h2>
            <div class="flex items-center space-x-4">
              <div class="flex items-center space-x-2">
                <Icon name="lucide:leaf" class="w-5 h-5" />
                <span class="text-3xl font-bold">{{ overallPercentage }}%</span>
              </div>
              <div class="text-sm opacity-90">
                <p>{{ Math.round(totalCurrentLevel) }}L Available</p>
                <p>20 Hours Remaining</p>
              </div>
            </div>
          </div>
          <div class="w-24 h-24 relative">
            <svg class="w-24 h-24 transform -rotate-90" viewBox="0 0 100 100">
              <circle cx="50" cy="50" r="40" stroke="rgba(255,255,255,0.2)" stroke-width="8" fill="none" />
              <circle
                cx="50"
                cy="50"
                r="40"
                stroke="white"
                stroke-width="8"
                fill="none"
                :stroke-dasharray="`${overallPercentage * 2.51} 251`"
                class="transition-all duration-500"
              />
            </svg>
            <div class="absolute inset-0 flex items-center justify-center">
              <Icon name="lucide:droplets" class="w-8 h-8 text-white" />
            </div>
          </div>
        </div>
      </div>

      <!-- Recent Activity -->
      <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 mb-20">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Recent Activity</h3>
            <button class="text-sm text-blue-500 hover:text-blue-600">See all</button>
          </div>
        </div>

        <div class="divide-y divide-gray-200 dark:divide-gray-700">
          <div class="p-6 flex items-center justify-between hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors">
            <div class="flex items-center space-x-4">
              <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
                <Icon name="lucide:droplets" class="w-5 h-5 text-blue-500" />
              </div>
              <div>
                <h4 class="font-medium text-gray-900 dark:text-white">Main Tank</h4>
                <p class="text-sm text-gray-500 dark:text-gray-400">8h 50m | 2 Devices</p>
              </div>
            </div>
            <div class="text-right">
              <p class="font-medium text-gray-900 dark:text-white">750L</p>
            </div>
          </div>

          <div class="p-6 flex items-center justify-between hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors">
            <div class="flex items-center space-x-4">
              <div class="w-10 h-10 bg-yellow-100 dark:bg-yellow-900/20 rounded-full flex items-center justify-center">
                <Icon name="lucide:droplets" class="w-5 h-5 text-yellow-500" />
              </div>
              <div>
                <h4 class="font-medium text-gray-900 dark:text-white">Reserve Tank</h4>
                <p class="text-sm text-gray-500 dark:text-gray-400">2h 30m | 2 Devices</p>
              </div>
            </div>
            <div class="text-right">
              <p class="font-medium text-gray-900 dark:text-white">225L</p>
            </div>
          </div>
        </div>

        <!-- Add Device Button -->
        <div class="p-6 border-t border-gray-200 dark:border-gray-700">
          <button class="w-12 h-12 bg-blue-500 hover:bg-blue-600 rounded-full flex items-center justify-center text-white transition-colors">
            <Icon name="lucide:plus" class="w-6 h-6" />
          </button>
        </div>
      </div>
    </div>

    <!-- Bottom Navigation -->
    <div class="fixed bottom-0 left-0 right-0 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 px-4 py-2">
      <div class="max-w-7xl mx-auto">
        <div class="flex items-center justify-around">
          <button class="flex flex-col items-center space-y-1 p-2 text-blue-500">
            <Icon name="lucide:home" class="w-6 h-6" />
            <span class="text-xs">Home</span>
          </button>
          <button class="flex flex-col items-center space-y-1 p-2 text-gray-400 dark:text-gray-500">
            <Icon name="lucide:grid-3x3" class="w-6 h-6" />
            <span class="text-xs">Devices</span>
          </button>
          <button class="flex flex-col items-center space-y-1 p-2 text-gray-400 dark:text-gray-500">
            <Icon name="lucide:bar-chart-3" class="w-6 h-6" />
            <span class="text-xs">Analytics</span>
          </button>
          <button class="flex flex-col items-center space-y-1 p-2 text-gray-400 dark:text-gray-500">
            <Icon name="lucide:user" class="w-6 h-6" />
            <span class="text-xs">Profile</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>