<script setup lang="ts">
// Import stores
import { useOrganizationStore } from '~/stores/organization/organization.store'
import { useLocationsStore } from '~/stores/location/locations.store'
import { useTanksStore } from '~/stores/tanks/tanks.store'

// Stores
const organizationStore = useOrganizationStore()
const locationsStore = useLocationsStore()
const tanksStore = useTanksStore()

// Navigation
const handleLocationClick = (location: any) => {
  navigateTo(`/location/${location.id}`)
}

const handleTankClick = (tank: any) => {
  navigateTo(`/tank/${tank.id}`)
}

const handleViewAllActivities = () => {
  // Navigate to activities page or show modal
  console.log('View all activities')
}

// Initialize data
onMounted(async () => {
  locationsStore.fetchLocations()
})

</script>

<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Header -->
    <AppHeader title="WataBot"
      :subtitle="`${organizationStore.organizationName} • ${organizationStore.organizationPlan}`" />

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Greeting Section -->
      <div class="mb-8">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">Good morning, Admin</h2>
        <p class="text-gray-600 dark:text-gray-400">Monitor your water tanks and stay informed about system status.</p>
      </div>

      <!-- Tank Status Overview -->
      <div class="mb-8">
        <TankStatusOverview :stats="tanksStore.tankStats" />
      </div>

      <!-- Main Content Grid -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Tank Status Cards -->
        <div class="lg:col-span-2">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white">Tank Status</h3>
            <button class="text-sm text-blue-500 hover:text-blue-600 font-medium">View all tanks</button>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <TankStatusCard v-for="tank in tanksStore.allTanks" :key="tank.id" :tank="tank" :show-details="true"
              @click="handleTankClick" />
          </div>
        </div>

        <!-- Locations Section -->
        <div class="mt-12">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white">Locations</h3>
            <button class="text-sm text-blue-500 hover:text-blue-600 font-medium">Manage locations</button>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <LocationCard v-for="location in locationsStore.allLocations" :key="location.id" :location="location"
              @click="handleLocationClick" />
          </div>
        </div>

        <!-- Activity Feed -->
        <div class="space-y-6">
          <ActivityFeed :activities="tanksStore.getRecentActivities()" @view-all="handleViewAllActivities" />
        </div>
      </div>


    </div>

    <!-- Bottom Navigation -->
    <!-- <BottomNavigation current-page="home" /> -->
  </div>
</template>