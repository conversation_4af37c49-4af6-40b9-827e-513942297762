<script setup lang="ts">
// Stores
const organizationStore = useOrganizationStore()
const locationsStore = useLocationsStore()

// Navigation
const handleLocationClick = (location: any) => {
  navigateTo(`/location/${location.id}`)
}

// Initialize data
onMounted(() => {
  locationsStore.fetchLocations()
})
</script>

<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Header -->
    <AppHeader
      title="WataBot"
      :subtitle="`${organizationStore.organizationPlan}`"
    />

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Greeting Section -->
      <div class="mb-8">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">Good morning, Admin</h2>
        <p class="text-gray-600 dark:text-gray-400">Here's what's happening with your water monitoring system today.</p>
      </div>

      <!-- Overview Stats -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
              <Icon name="lucide:droplets" class="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
            <div class="ml-4">
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ locationsStore.totalTanks }}</p>
              <p class="text-sm text-gray-500 dark:text-gray-400">Total Tanks</p>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center">
              <Icon name="lucide:activity" class="w-6 h-6 text-green-600 dark:text-green-400" />
            </div>
            <div class="ml-4">
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ organizationStore.activeSensors }}</p>
              <p class="text-sm text-gray-500 dark:text-gray-400">Active Sensors</p>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/20 rounded-full flex items-center justify-center">
              <Icon name="lucide:map-pin" class="w-6 h-6 text-purple-600 dark:text-purple-400" />
            </div>
            <div class="ml-4">
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ locationsStore.onlineLocations.length }}</p>
              <p class="text-sm text-gray-500 dark:text-gray-400">Online Locations</p>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-orange-100 dark:bg-orange-900/20 rounded-full flex items-center justify-center">
              <Icon name="lucide:gauge" class="w-6 h-6 text-orange-600 dark:text-orange-400" />
            </div>
            <div class="ml-4">
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ locationsStore.averageLevel }}%</p>
              <p class="text-sm text-gray-500 dark:text-gray-400">Avg Level</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Locations Grid -->
      <div class="mb-8">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-xl font-semibold text-gray-900 dark:text-white">Locations</h3>
          <button class="text-sm text-blue-500 hover:text-blue-600 font-medium">View all</button>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <LocationCard
            v-for="location in locationsStore.allLocations"
            :key="location.id"
            :location="location"
            @click="handleLocationClick"
          />
        </div>
      </div>
    </div>

    <!-- Bottom Navigation -->
    <BottomNavigation current-page="home" />
  </div>
</template>