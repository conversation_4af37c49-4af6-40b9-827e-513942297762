<script setup lang="ts">
// Import stores
import { useTanksStore } from '~/stores/tanks/tanks.store'

// Stores
const tanksStore = useTanksStore()

// Route
const route = useRoute()
const tankId = parseInt(route.params.id as string)

// Data
const tank = computed(() => tanksStore.getTankById(tankId))
const currentReading = computed(() => tanksStore.getCurrentReading(tankId))
const usageData = computed(() => tanksStore.getUsageData(tankId))
const connectedDevices = computed(() => tanksStore.getConnectedDevices(tankId))

// Real-time updates
let realtimeInterval: NodeJS.Timeout

// Navigation
const goBack = () => {
  navigateTo('/location/1') // Navigate back to location
}


// Event handlers
const handleAddSensor = () => {
  console.log('Add sensor clicked')
}

const handleManageSensors = () => {
  console.log('Manage sensors clicked')
}
</script>

<template>
  <div v-if="tank && currentReading" class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Header -->
    <AppHeader
      :title="tank.name"
      :subtitle="tank.location"
      :show-back-button="true"
      :online-status="tank.isOnline"
      @back="goBack"
    />

    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <!-- Real-time Monitoring -->
      <RealTimeMonitoring
        :tank="tank"
        :current-reading="currentReading"
      />

      <!-- Usage Analytics -->
      <UsageAnalytics
        :usage-data="usageData"
        :monthly-usage="tank.monthlyUsage"
        :monthly-expenses="tank.monthlyExpenses"
      />

      <!-- Connected Sensors -->
      <ConnectedSensors
        :devices="connectedDevices"
        @add-sensor="handleAddSensor"
        @manage-sensors="handleManageSensors"
      />

    </div>

    <!-- Bottom Navigation -->
    <BottomNavigation current-page="analytics" />
  </div>
</template>
