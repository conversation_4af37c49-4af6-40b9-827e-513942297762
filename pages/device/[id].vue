<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import type { WaterDevice, UsageData, ConnectedDevice, DeviceStatus, Organization, RealTimeReading } from '~/types'

const route = useRoute()
const deviceId = route.params.id

// Organization data
const currentOrganization = ref<Organization>({
  name: 'Acme Manufacturing Ltd',
  logo: 'https://images.unsplash.com/photo-1560179707-f14e90ef3623?w=40&h=40&fit=crop&auto=format',
  plan: 'Enterprise',
  totalTanks: 4,
  activeSensors: 7
})

// Mock device data with tank details
const device = ref<WaterDevice & { monthlyUsage: number; monthlyExpenses: number }>({
  id: parseInt(deviceId as string),
  name: 'Main Water Tank',
  location: 'Rooftop',
  currentLevel: 75,
  maxCapacity: 1000,
  status: 'normal' as DeviceStatus,
  lastUpdated: new Date(),
  monthlyUsage: 350,
  monthlyExpenses: 125,
  icon: 'droplets',
  isOnline: true,
  deviceCount: 2,
  tankBrand: 'Sintex',
  tankHeight: 2.5,
  tankVolume: 1000
})

// Real-time readings
const realTimeReadings = ref<RealTimeReading[]>([])
const currentReading = ref<RealTimeReading>({
  timestamp: new Date(),
  level: 75,
  volume: 750,
  temperature: 24.5,
  ph: 7.2
})

// Simulate real-time updates
let realtimeInterval: NodeJS.Timeout

const updateRealTimeData = () => {
  const newReading: RealTimeReading = {
    timestamp: new Date(),
    level: device.value.currentLevel + (Math.random() - 0.5) * 2, // Small fluctuation
    volume: Math.round((device.value.currentLevel / 100) * device.value.maxCapacity),
    temperature: 24 + (Math.random() - 0.5) * 3,
    ph: 7 + (Math.random() - 0.5) * 0.5
  }

  // Keep level within bounds
  newReading.level = Math.max(0, Math.min(100, newReading.level))
  newReading.volume = Math.round((newReading.level / 100) * device.value.maxCapacity)

  currentReading.value = newReading
  device.value.currentLevel = newReading.level
  device.value.lastUpdated = newReading.timestamp

  // Keep only last 20 readings
  realTimeReadings.value.unshift(newReading)
  if (realTimeReadings.value.length > 20) {
    realTimeReadings.value.pop()
  }
}

onMounted(() => {
  // Initialize with some historical data
  for (let i = 19; i >= 0; i--) {
    realTimeReadings.value.push({
      timestamp: new Date(Date.now() - i * 30000), // 30 seconds apart
      level: 75 + (Math.random() - 0.5) * 5,
      volume: 750 + (Math.random() - 0.5) * 50,
      temperature: 24 + (Math.random() - 0.5) * 2,
      ph: 7.2 + (Math.random() - 0.5) * 0.3
    })
  }

  // Start real-time updates every 5 seconds
  realtimeInterval = setInterval(updateRealTimeData, 5000)
})

onUnmounted(() => {
  if (realtimeInterval) {
    clearInterval(realtimeInterval)
  }
})

// Mock usage data for the chart
const usageData = ref<UsageData[]>([
  { month: 'Jan', usage: 280 },
  { month: 'Feb', usage: 320 },
  { month: 'Mar', usage: 290 },
  { month: 'Apr', usage: 350 },
  { month: 'May', usage: 380 },
  { month: 'Jun', usage: 340 },
  { month: 'Jul', usage: 360 }
])

// Mock connected devices/sensors
const connectedDevices = ref<ConnectedDevice[]>([
  {
    name: 'Level Sensor',
    duration: '2h 30m',
    deviceCount: 2,
    usage: '10kWh',
    icon: 'gauge'
  },
  {
    name: 'Flow Meter',
    duration: '2h 30m',
    deviceCount: 2,
    usage: '5kWh',
    icon: 'activity'
  },
  {
    name: 'Pump Controller',
    duration: '8h 50m',
    deviceCount: 2,
    usage: '15kWh',
    icon: 'zap'
  }
])

const currentUsage = computed(() => Math.round(device.value.currentLevel / 100 * device.value.maxCapacity))
const maxPoint = computed(() => Math.max(...usageData.value.map(d => d.usage)))

const getStatusColor = (status: DeviceStatus) => {
  switch (status) {
    case 'normal': return 'text-green-500'
    case 'low': return 'text-yellow-500'
    case 'critical': return 'text-red-500'
    default: return 'text-gray-500'
  }
}

const goBack = () => {
  navigateTo('/')
}
</script>

<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Header -->
    <div class="bg-white dark:bg-gray-800 shadow-sm">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between h-16">
          <div class="flex items-center space-x-4">
            <button @click="goBack" class="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg">
              <Icon name="lucide:arrow-left" class="w-5 h-5 text-gray-600 dark:text-gray-300" />
            </button>
            <div>
              <h1 class="text-xl font-semibold text-gray-900 dark:text-white">{{ device.name }}</h1>
              <p class="text-sm text-gray-500 dark:text-gray-400">{{ currentOrganization.name }} • {{ device.location }}</p>
            </div>
          </div>
          <div class="flex items-center space-x-4">
            <div class="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-300">
              <div :class="['w-2 h-2 rounded-full', device.isOnline ? 'bg-green-500' : 'bg-red-500']"></div>
              <span>{{ device.isOnline ? 'ONLINE' : 'OFFLINE' }}</span>
            </div>
            <button class="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg">
              <Icon name="lucide:more-horizontal" class="w-5 h-5 text-gray-600 dark:text-gray-300" />
            </button>
          </div>
        </div>
      </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Real-time Water Level Status -->
      <div class="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 p-6 mb-8">
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Real-time Water Level</h2>
          <div class="flex items-center space-x-2 text-sm text-green-600 dark:text-green-400">
            <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span>Live</span>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
          <!-- Current Level -->
          <div class="text-center">
            <div class="text-3xl font-bold text-gray-900 dark:text-white mb-1">{{ Math.round(currentReading.level) }}%</div>
            <div class="text-sm text-gray-500 dark:text-gray-400">Water Level</div>
          </div>

          <!-- Current Volume -->
          <div class="text-center">
            <div class="text-3xl font-bold text-blue-600 dark:text-blue-400 mb-1">{{ Math.round(currentReading.volume) }}L</div>
            <div class="text-sm text-gray-500 dark:text-gray-400">Current Volume</div>
          </div>

          <!-- Temperature -->
          <div class="text-center">
            <div class="text-3xl font-bold text-orange-600 dark:text-orange-400 mb-1">{{ currentReading.temperature?.toFixed(1) }}°C</div>
            <div class="text-sm text-gray-500 dark:text-gray-400">Temperature</div>
          </div>

          <!-- pH Level -->
          <div class="text-center">
            <div class="text-3xl font-bold text-purple-600 dark:text-purple-400 mb-1">{{ currentReading.ph?.toFixed(1) }}</div>
            <div class="text-sm text-gray-500 dark:text-gray-400">pH Level</div>
          </div>
        </div>

        <!-- Tank Details -->
        <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 mb-6">
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span class="text-gray-500 dark:text-gray-400">Tank Brand:</span>
              <div class="font-medium text-gray-900 dark:text-white">{{ device.tankBrand }}</div>
            </div>
            <div>
              <span class="text-gray-500 dark:text-gray-400">Height:</span>
              <div class="font-medium text-gray-900 dark:text-white">{{ device.tankHeight }}m</div>
            </div>
            <div>
              <span class="text-gray-500 dark:text-gray-400">Capacity:</span>
              <div class="font-medium text-gray-900 dark:text-white">{{ device.tankVolume }}L</div>
            </div>
            <div>
              <span class="text-gray-500 dark:text-gray-400">Sensors:</span>
              <div class="font-medium text-gray-900 dark:text-white">{{ device.deviceCount }} Active</div>
            </div>
          </div>
        </div>

        <!-- Visual Level Indicator -->
        <div class="flex items-center space-x-4">
          <div class="flex-1">
            <div class="flex justify-between items-center mb-2">
              <span class="text-sm text-gray-500 dark:text-gray-400">Water Level</span>
              <span class="text-sm font-medium text-gray-900 dark:text-white">{{ Math.round(currentReading.level) }}%</span>
            </div>
            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-4">
              <div
                :class="['h-4 rounded-full transition-all duration-500', getStatusColor(device.status).replace('text-', 'bg-')]"
                :style="{ width: `${currentReading.level}%` }"
              ></div>
            </div>
          </div>
          <div class="w-16 h-32 bg-gray-200 dark:bg-gray-700 rounded-lg relative overflow-hidden">
            <div
              :class="['absolute bottom-0 left-0 right-0 rounded-lg transition-all duration-500', getStatusColor(device.status).replace('text-', 'bg-')]"
              :style="{ height: `${currentReading.level}%` }"
            ></div>
            <div class="absolute inset-0 flex items-center justify-center">
              <Icon name="lucide:droplets" class="w-6 h-6 text-white" />
            </div>
          </div>
        </div>

        <div class="mt-4 text-xs text-gray-500 dark:text-gray-400 text-center">
          Last updated: {{ currentReading.timestamp.toLocaleTimeString() }}
        </div>
      </div>

      <!-- Usage Period Selector -->
      <div class="flex items-center justify-between mb-8">
        <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Historical Usage</h2>
        <select class="px-3 py-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg text-sm text-gray-700 dark:text-gray-200">
          <option>Monthly</option>
          <option>Weekly</option>
          <option>Daily</option>
        </select>
      </div>

      <!-- Usage Chart -->
      <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 mb-8">
        <div class="relative h-64">
          <!-- Y-axis labels -->
          <div class="absolute left-0 top-0 h-full flex flex-col justify-between text-xs text-gray-500 dark:text-gray-400">
            <span>{{ maxPoint + 50 }}L</span>
            <span>{{ Math.round(maxPoint * 0.75) }}L</span>
            <span>{{ Math.round(maxPoint * 0.5) }}L</span>
            <span>{{ Math.round(maxPoint * 0.25) }}L</span>
            <span>0L</span>
          </div>
          
          <!-- Chart area -->
          <div class="ml-12 h-full relative">
            <!-- Grid lines -->
            <div class="absolute inset-0 flex flex-col justify-between">
              <div v-for="i in 5" :key="i" class="border-t border-gray-100 dark:border-gray-700"></div>
            </div>
            
            <!-- Chart line -->
            <svg class="absolute inset-0 w-full h-full" viewBox="0 0 400 200">
              <defs>
                <linearGradient id="gradient" x1="0%" y1="0%" x2="0%" y2="100%">
                  <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:0.3" />
                  <stop offset="100%" style="stop-color:#3B82F6;stop-opacity:0" />
                </linearGradient>
              </defs>
              
              <!-- Area under curve -->
              <path 
                :d="`M 0 ${200 - (usageData[0].usage / maxPoint * 150)} ${usageData.map((d, i) => `L ${i * 60} ${200 - (d.usage / maxPoint * 150)}`).join(' ')} L ${(usageData.length - 1) * 60} 200 L 0 200 Z`"
                fill="url(#gradient)"
              />
              
              <!-- Line -->
              <path 
                :d="`M 0 ${200 - (usageData[0].usage / maxPoint * 150)} ${usageData.map((d, i) => `L ${i * 60} ${200 - (d.usage / maxPoint * 150)}`).join(' ')}`"
                stroke="#3B82F6" 
                stroke-width="3" 
                fill="none"
                class="drop-shadow-sm"
              />
              
              <!-- Data points -->
              <circle 
                v-for="(point, index) in usageData" 
                :key="index"
                :cx="index * 60" 
                :cy="200 - (point.usage / maxPoint * 150)"
                r="4" 
                fill="#3B82F6"
                class="drop-shadow-sm"
              />
              
              <!-- Current month highlight -->
              <circle 
                :cx="(usageData.length - 2) * 60" 
                :cy="200 - (usageData[usageData.length - 2].usage / maxPoint * 150)"
                r="6" 
                fill="white"
                stroke="#3B82F6"
                stroke-width="3"
                class="drop-shadow-md"
              />
            </svg>
            
            <!-- Current usage indicator -->
            <div 
              class="absolute bg-blue-500 text-white px-3 py-1 rounded-lg text-sm font-medium shadow-lg"
              :style="{ 
                left: `${(usageData.length - 2) * 60 - 20}px`, 
                top: `${200 - (usageData[usageData.length - 2].usage / maxPoint * 150) - 40}px` 
              }"
            >
              {{ usageData[usageData.length - 2].usage }}L
            </div>
          </div>
          
          <!-- X-axis labels -->
          <div class="absolute bottom-0 left-12 right-0 flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-4">
            <span v-for="point in usageData" :key="point.month">{{ point.month }}</span>
          </div>
        </div>
      </div>

      <!-- Usage Stats -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
          <div class="flex items-center space-x-4">
            <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
              <Icon name="lucide:droplets" class="w-6 h-6 text-blue-500" />
            </div>
            <div>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ device.monthlyUsage }}L</p>
              <p class="text-sm text-gray-500 dark:text-gray-400">Water usage</p>
            </div>
          </div>
        </div>
        
        <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
          <div class="flex items-center space-x-4">
            <div class="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center">
              <Icon name="lucide:dollar-sign" class="w-6 h-6 text-green-500" />
            </div>
            <div>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">${{ device.monthlyExpenses }}</p>
              <p class="text-sm text-gray-500 dark:text-gray-400">Monthly cost</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Connected Devices -->
      <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Connected Devices</h3>
            <button class="text-sm text-blue-500 hover:text-blue-600">See all</button>
          </div>
        </div>
        
        <div class="divide-y divide-gray-200 dark:divide-gray-700">
          <div 
            v-for="connectedDevice in connectedDevices" 
            :key="connectedDevice.name"
            class="p-6 flex items-center justify-between hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors"
          >
            <div class="flex items-center space-x-4">
              <div class="w-10 h-10 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
                <Icon :name="`lucide:${connectedDevice.icon}`" class="w-5 h-5 text-gray-600 dark:text-gray-300" />
              </div>
              <div>
                <h4 class="font-medium text-gray-900 dark:text-white">{{ connectedDevice.name }}</h4>
                <p class="text-sm text-gray-500 dark:text-gray-400">{{ connectedDevice.duration }} | {{ connectedDevice.deviceCount }} Devices</p>
              </div>
            </div>
            <div class="text-right">
              <p class="font-medium text-gray-900 dark:text-white">{{ connectedDevice.usage }}</p>
            </div>
          </div>
        </div>
        
        <!-- Add Device Button -->
        <div class="p-6 border-t border-gray-200 dark:border-gray-700 mb-20">
          <button class="w-12 h-12 bg-blue-500 hover:bg-blue-600 rounded-full flex items-center justify-center text-white transition-colors">
            <Icon name="lucide:plus" class="w-6 h-6" />
          </button>
        </div>
      </div>
    </div>

    <!-- Bottom Navigation -->
    <div class="fixed bottom-0 left-0 right-0 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 px-4 py-2">
      <div class="max-w-7xl mx-auto">
        <div class="flex items-center justify-around">
          <button @click="goBack" class="flex flex-col items-center space-y-1 p-2 text-blue-500">
            <Icon name="lucide:home" class="w-6 h-6" />
            <span class="text-xs">Home</span>
          </button>
          <button class="flex flex-col items-center space-y-1 p-2 text-gray-400 dark:text-gray-500">
            <Icon name="lucide:grid-3x3" class="w-6 h-6" />
            <span class="text-xs">Devices</span>
          </button>
          <button class="flex flex-col items-center space-y-1 p-2 text-blue-500">
            <Icon name="lucide:bar-chart-3" class="w-6 h-6" />
            <span class="text-xs">Analytics</span>
          </button>
          <button class="flex flex-col items-center space-y-1 p-2 text-gray-400 dark:text-gray-500">
            <Icon name="lucide:user" class="w-6 h-6" />
            <span class="text-xs">Profile</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>
