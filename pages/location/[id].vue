<script setup lang="ts">
import { ref, computed } from 'vue'
import type { Location, Tank, DeviceStatus, Organization } from '~/types'

const route = useRoute()
const locationId = parseInt(route.params.id as string)

// Organization data
const currentOrganization = ref<Organization>({
  name: 'Acme Manufacturing Ltd',
  logo: 'https://images.unsplash.com/photo-1560179707-f14e90ef3623?w=40&h=40&fit=crop&auto=format',
  plan: 'Enterprise',
  totalTanks: 4,
  activeSensors: 7
})

// Mock location data
const location = ref<Location>({
  id: locationId,
  name: 'Rooftop',
  description: 'Main building rooftop water storage',
  icon: 'building',
  tanks: [
    {
      id: 101,
      name: 'Main Water Tank',
      brand: 'Sintex',
      height: 2.5,
      volume: 1000,
      currentLevel: 75,
      status: 'normal',
      lastUpdated: new Date(Date.now() - 5 * 60000),
      isOnline: true,
      sensorId: 'SNS-001'
    },
    {
      id: 102,
      name: 'Backup Tank',
      brand: 'Sintex',
      height: 2.0,
      volume: 800,
      currentLevel: 60,
      status: 'normal',
      lastUpdated: new Date(Date.now() - 3 * 60000),
      isOnline: true,
      sensorId: 'SNS-002'
    }
  ]
})

// Helper functions
const formatTimeAgo = (date: Date) => {
  const now = new Date()
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))
  
  if (diffInMinutes < 60) {
    return `${diffInMinutes}m ago`
  } else if (diffInMinutes < 1440) {
    return `${Math.floor(diffInMinutes / 60)}h ago`
  } else {
    return `${Math.floor(diffInMinutes / 1440)}d ago`
  }
}

const getStatusColor = (status: DeviceStatus) => {
  switch (status) {
    case 'normal': return 'text-green-500'
    case 'low': return 'text-yellow-500'
    case 'critical': return 'text-red-500'
    default: return 'text-gray-500'
  }
}

const goBack = () => {
  navigateTo('/')
}

const navigateToTank = (tankId: number) => {
  navigateTo(`/tank/${tankId}`)
}

// Computed properties
const totalCapacity = computed(() => location.value.tanks.reduce((sum, tank) => sum + tank.volume, 0))
const totalCurrentVolume = computed(() => location.value.tanks.reduce((sum, tank) => sum + (tank.currentLevel / 100 * tank.volume), 0))
const averageLevel = computed(() => {
  const onlineTanks = location.value.tanks.filter(tank => tank.isOnline)
  if (onlineTanks.length === 0) return 0
  return Math.round(onlineTanks.reduce((sum, tank) => sum + tank.currentLevel, 0) / onlineTanks.length)
})
const onlineTanks = computed(() => location.value.tanks.filter(tank => tank.isOnline).length)
</script>

<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Header -->
    <div class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between h-16">
          <div class="flex items-center space-x-4">
            <button @click="goBack" class="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg">
              <Icon name="lucide:arrow-left" class="w-5 h-5 text-gray-600 dark:text-gray-300" />
            </button>
            <div>
              <h1 class="text-xl font-semibold text-gray-900 dark:text-white">{{ location.name }}</h1>
              <p class="text-sm text-gray-500 dark:text-gray-400">{{ currentOrganization.name }} • {{ location.description }}</p>
            </div>
          </div>
          <div class="flex items-center space-x-4">
            <div class="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-300">
              <Icon name="lucide:droplets" class="w-4 h-4" />
              <span>{{ onlineTanks }} of {{ location.tanks.length }} tanks online</span>
            </div>
            <button class="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg">
              <Icon name="lucide:more-horizontal" class="w-5 h-5 text-gray-600 dark:text-gray-300" />
            </button>
          </div>
        </div>
      </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Location Overview -->
      <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-2xl p-6 text-white mb-8">
        <div class="flex items-center justify-between">
          <div>
            <h2 class="text-lg font-medium mb-2">{{ location.name }} Overview</h2>
            <div class="flex items-center space-x-4">
              <div class="flex items-center space-x-2">
                <Icon name="lucide:droplets" class="w-5 h-5" />
                <span class="text-3xl font-bold">{{ Math.round(totalCurrentVolume) }}L</span>
              </div>
              <div class="text-sm opacity-90">
                <p>{{ averageLevel }}% average level</p>
                <p>{{ totalCapacity }}L total capacity</p>
              </div>
            </div>
          </div>
          <div class="w-24 h-24 relative">
            <svg class="w-24 h-24 transform -rotate-90" viewBox="0 0 100 100">
              <circle cx="50" cy="50" r="40" stroke="rgba(255,255,255,0.2)" stroke-width="8" fill="none" />
              <circle 
                cx="50" 
                cy="50" 
                r="40" 
                stroke="white" 
                stroke-width="8" 
                fill="none"
                :stroke-dasharray="`${averageLevel * 2.51} 251`"
                class="transition-all duration-500"
              />
            </svg>
            <div class="absolute inset-0 flex items-center justify-center">
              <Icon name="lucide:gauge" class="w-8 h-8 text-white" />
            </div>
          </div>
        </div>
      </div>

      <!-- Tanks Grid -->
      <div class="mb-8">
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Water Tanks</h2>
          <button class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
            <Icon name="lucide:plus" class="w-4 h-4 mr-2 inline" />
            Add Tank
          </button>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div 
            v-for="tank in location.tanks" 
            :key="tank.id"
            @click="navigateToTank(tank.id)"
            class="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-all duration-200"
          >
            <div class="flex items-center justify-between mb-4">
              <div class="relative">
                <div class="w-12 h-12 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
                  <Icon name="lucide:droplets" class="w-6 h-6 text-blue-500" />
                </div>
                <div 
                  :class="['absolute -top-1 -right-1 w-4 h-4 rounded-full border-2 border-white dark:border-gray-800', tank.isOnline ? 'bg-green-500' : 'bg-red-500']"
                ></div>
              </div>
              <div class="text-right">
                <div :class="['w-2 h-2 rounded-full mb-1 ml-auto', getStatusColor(tank.status).replace('text-', 'bg-')]"></div>
                <span class="text-xs text-gray-500 dark:text-gray-400 uppercase">{{ tank.isOnline ? 'ONLINE' : 'OFFLINE' }}</span>
              </div>
            </div>
            
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-1">{{ tank.name }}</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">{{ tank.brand }} • {{ tank.height }}m × {{ tank.volume }}L</p>
            
            <div class="space-y-3">
              <div class="flex justify-between items-center">
                <span class="text-2xl font-bold text-gray-900 dark:text-white">{{ tank.currentLevel }}%</span>
                <span class="text-sm text-gray-500 dark:text-gray-400">{{ Math.round(tank.currentLevel / 100 * tank.volume) }}L</span>
              </div>
              <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div 
                  :class="['h-2 rounded-full transition-all duration-500', getStatusColor(tank.status).replace('text-', 'bg-')]"
                  :style="{ width: `${tank.currentLevel}%` }"
                ></div>
              </div>
              <div class="flex justify-between items-center text-sm text-gray-500 dark:text-gray-400">
                <span>Sensor: {{ tank.sensorId }}</span>
                <span>{{ formatTimeAgo(tank.lastUpdated) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Bottom Navigation -->
    <div class="fixed bottom-0 left-0 right-0 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 px-4 py-2">
      <div class="max-w-7xl mx-auto">
        <div class="flex items-center justify-around">
          <button @click="goBack" class="flex flex-col items-center space-y-1 p-2 text-blue-500">
            <Icon name="lucide:home" class="w-6 h-6" />
            <span class="text-xs">Home</span>
          </button>
          <button class="flex flex-col items-center space-y-1 p-2 text-blue-500">
            <Icon name="lucide:grid-3x3" class="w-6 h-6" />
            <span class="text-xs">Tanks</span>
          </button>
          <button class="flex flex-col items-center space-y-1 p-2 text-gray-400 dark:text-gray-500">
            <Icon name="lucide:bar-chart-3" class="w-6 h-6" />
            <span class="text-xs">Analytics</span>
          </button>
          <button class="flex flex-col items-center space-y-1 p-2 text-gray-400 dark:text-gray-500">
            <Icon name="lucide:user" class="w-6 h-6" />
            <span class="text-xs">Profile</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>
