import { createClient } from '@supabase/supabase-js'

// Direct configuration to avoid runtime config issues
const supabaseUrl = 'https://xwkrufpcgaxkmhqqvjyv.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inh3a3J1ZnBjZ2F4a21ocXF2anl2Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczMTMzMzM1NywiZXhwIjoyMDQ2OTA5MzU3fQ.lktmhYK4EAiPjEWat-p2yhfBlAkAHqNUTf10WwDIjck'

export const supabase = createClient(supabaseUrl, supabaseKey)

// Database types
export interface DeviceMetric {
  id: number
  device_id: string
  tank_height: number
  timestamp: string
  created_at: string
  updated_at: string
}

// Tank configuration for WASL Main Tank
export const WASL_TANK_CONFIG = {
  device_id: 'wasl-main-tank',
  tank_id: 101,
  max_height: 2.5, // meters
  volume: 2000, // liters
  name: 'WASL Main Tank'
}

// Helper function to calculate water level percentage from tank height
export const calculateWaterLevel = (tankHeight: number, maxHeight: number = WASL_TANK_CONFIG.max_height): number => {
  if (tankHeight <= 0) return 0
  if (tankHeight >= maxHeight) return 100
  return Math.round((tankHeight / maxHeight) * 100)
}

// Helper function to calculate volume from height
export const calculateVolume = (tankHeight: number, maxVolume: number = WASL_TANK_CONFIG.volume): number => {
  const percentage = calculateWaterLevel(tankHeight)
  return Math.round((percentage / 100) * maxVolume)
}

// Helper function to determine tank status based on level
export const getTankStatus = (level: number): 'full' | 'normal' | 'low' | 'critical' | 'empty' => {
  if (level === 0) return 'empty'
  if (level >= 95) return 'full'
  if (level < 10) return 'critical'
  if (level < 25) return 'low'
  return 'normal'
}
